"""
Core module for the work monitor system.

This module contains the core components of the chain-of-responsibility
architecture including handlers, context, and execution management.
"""

from .context import WorkContext, ClientConfig
from .exceptions import WorkMonitorException, HandlerException
from .handlers.base_handler import <PERSON>Handler
from .chain_executor import ChainExecutor

__all__ = [
    'WorkContext',
    'ClientConfig', 
    'WorkMonitorException',
    'HandlerException',
    'BaseHandler',
    'ChainExecutor'
]
