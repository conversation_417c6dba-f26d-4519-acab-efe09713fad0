#!/usr/bin/env python3
"""
Test script for simplified handler usage.
"""

import logging
import sys
from unittest.mock import Mock

# Add current directory to path for imports
sys.path.insert(0, '.')

from core.context import WorkContext, ClientConfig
from core.handlers import (
    WorkUpdater<PERSON><PERSON><PERSON>,
    DingTalkNotification,
    WebhookDataSync,
    create_data_fetcher,
    create_work_updater,
    create_data_sync,
    create_notification,
    DefaultChainBuilder,
    CoreHandlerFactory
)
from clients.shuiyuntu.chain_builder import build_shuiyuntu_chain
from clients.shuiyuntu.handlers import ShuiyuntuWorkUpdater


def setup_test_logging():
    """Setup logging for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger('test_simplified')


def create_test_config():
    """Create a test client configuration."""
    config_data = {
        'name': 'test-client',
        'data_fetcher': {'source_type': 'dingtalk_bitable'},
        'work_updater': {'platforms': ['xhs', 'dy'], 'batch_size': 10},
        'data_sync': {'sync_type': 'webhook', 'batch_size': 5},
        'notification': {'enabled_channels': ['dingtalk']},
        'user_info': {'api-token': 'test-token'},
        'bitable': {'dentryUuid': 'test-uuid', 'idOrName': 'test-table', 'webhook': 'https://test.com'}
    }
    return ClientConfig.from_dict(config_data)


def test_direct_inheritance():
    """Test direct inheritance approach."""
    print("=" * 60)
    print("Testing Direct Inheritance")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        # Define custom handler by direct inheritance
        class TestCustomWorkUpdater(WorkUpdaterHandler):
            def _process_single_task(self, context, task):
                self.logger.info(f"TestCustom processing: {task.work_url}")
                result = super()._process_single_task(context, task)
                
                # Add custom metadata
                if result.success and result.task.author_work:
                    if not result.task.metadata:
                        result.task.metadata = {}
                    result.task.metadata['test_custom_processed'] = True
                
                return result
        
        config = create_test_config()
        
        # Create custom handler directly
        custom_updater = TestCustomWorkUpdater(config, logger)
        
        assert custom_updater is not None
        assert isinstance(custom_updater, WorkUpdaterHandler)
        assert custom_updater.__class__.__name__ == 'TestCustomWorkUpdater'
        
        print("✅ Direct inheritance test passed")
        print(f"   Custom handler: {custom_updater.__class__.__name__}")
        print(f"   Inherits from: {custom_updater.__class__.__bases__[0].__name__}")
        
        return True
        
    except Exception as e:
        logger.error(f"Direct inheritance test failed: {e}")
        return False


def test_convenience_functions():
    """Test convenience functions."""
    print("\n" + "=" * 60)
    print("Testing Convenience Functions")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config()
        
        # Test convenience functions
        data_fetcher = create_data_fetcher(config, logger)
        work_updater = create_work_updater(config, logger, 'basic')
        data_sync = create_data_sync(config, logger)
        notification = create_notification(config, logger)
        
        # Verify types
        assert data_fetcher is not None
        assert work_updater is not None
        assert data_sync is not None
        assert notification is not None
        
        # Test auto-detection
        auto_data_sync = create_data_sync(config, logger)  # Should detect webhook
        auto_notification = create_notification(config, logger)  # Should detect dingtalk
        
        print("✅ Convenience functions test passed")
        print(f"   Data fetcher: {data_fetcher.__class__.__name__}")
        print(f"   Work updater: {work_updater.__class__.__name__}")
        print(f"   Data sync: {data_sync.__class__.__name__} (auto-detected)")
        print(f"   Notification: {notification.__class__.__name__} (auto-detected)")
        
        return True
        
    except Exception as e:
        logger.error(f"Convenience functions test failed: {e}")
        return False


def test_core_factory():
    """Test core factory usage."""
    print("\n" + "=" * 60)
    print("Testing Core Factory")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config()
        
        # Test all core factory methods
        data_fetcher = CoreHandlerFactory.create_dingtalk_data_fetcher(config, logger)
        work_updater = CoreHandlerFactory.create_database_work_updater(config, logger)
        data_sync = CoreHandlerFactory.create_webhook_data_sync(config, logger)
        notification = CoreHandlerFactory.create_dingtalk_notification(config, logger)
        
        # Verify types
        assert data_fetcher.__class__.__name__ == 'DingTalkDataFetcher'
        assert work_updater.__class__.__name__ == 'DatabaseWorkUpdater'
        assert data_sync.__class__.__name__ == 'WebhookDataSync'
        assert notification.__class__.__name__ == 'DingTalkNotification'
        
        print("✅ Core factory test passed")
        print(f"   Data fetcher: {data_fetcher.__class__.__name__}")
        print(f"   Work updater: {work_updater.__class__.__name__}")
        print(f"   Data sync: {data_sync.__class__.__name__}")
        print(f"   Notification: {notification.__class__.__name__}")
        
        return True
        
    except Exception as e:
        logger.error(f"Core factory test failed: {e}")
        return False


def test_default_chain_builder():
    """Test default chain builder."""
    print("\n" + "=" * 60)
    print("Testing Default Chain Builder")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config()
        
        # Test standard chain
        standard_chain = DefaultChainBuilder.build_standard_chain(config, logger)
        assert standard_chain is not None
        
        # Test basic chain
        basic_chain = DefaultChainBuilder.build_basic_chain(config, logger)
        assert basic_chain is not None
        
        # Verify chain structure
        current = standard_chain
        handlers = []
        while current:
            handlers.append(current.__class__.__name__)
            current = current.next_handler
        
        assert len(handlers) == 4  # data_fetcher -> work_updater -> data_sync -> notification
        
        print("✅ Default chain builder test passed")
        print(f"   Standard chain: {' -> '.join(handlers)}")
        print(f"   Chain length: {len(handlers)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Default chain builder test failed: {e}")
        return False


def test_client_specific_chain():
    """Test client-specific chain (Shuiyuntu example)."""
    print("\n" + "=" * 60)
    print("Testing Client-Specific Chain (Shuiyuntu)")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config_data = {
            'name': 'shuiyuntu',
            'user_info': {'api-token': 'test-token'},
            'bitable': {'dentryUuid': 'test-uuid', 'idOrName': 'test-table'}
        }
        config = ClientConfig.from_dict(config_data)
        
        # Test Shuiyuntu chain builder
        shuiyuntu_chain = build_shuiyuntu_chain(config, logger, 'standard')
        assert shuiyuntu_chain is not None
        
        # Verify custom handlers are used
        current = shuiyuntu_chain
        handlers = []
        while current:
            handlers.append(current.__class__.__name__)
            current = current.next_handler
        
        # Should include ShuiyuntuWorkUpdater
        assert 'ShuiyuntuWorkUpdater' in handlers
        
        print("✅ Client-specific chain test passed")
        print(f"   Shuiyuntu chain: {' -> '.join(handlers)}")
        print(f"   Custom handlers: {[h for h in handlers if 'Shuiyuntu' in h]}")
        
        return True
        
    except Exception as e:
        logger.error(f"Client-specific chain test failed: {e}")
        return False


def test_chain_composition():
    """Test manual chain composition."""
    print("\n" + "=" * 60)
    print("Testing Manual Chain Composition")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config()
        
        # Create handlers individually
        data_fetcher = create_data_fetcher(config, logger)
        work_updater = ShuiyuntuWorkUpdater(config, logger)  # Use custom handler
        data_sync = create_data_sync(config, logger)
        notification = create_notification(config, logger)
        
        # Compose chain manually
        data_fetcher.set_next(work_updater).set_next(data_sync).set_next(notification)
        chain_head = data_fetcher  # The head is the first handler

        # Verify chain
        assert chain_head == data_fetcher
        assert data_fetcher.next_handler == work_updater
        assert work_updater.next_handler == data_sync
        assert data_sync.next_handler == notification
        assert notification.next_handler is None
        
        # Verify custom handler
        assert isinstance(work_updater, ShuiyuntuWorkUpdater)
        
        print("✅ Manual chain composition test passed")
        print(f"   Chain: {data_fetcher.__class__.__name__} -> {work_updater.__class__.__name__} -> {data_sync.__class__.__name__} -> {notification.__class__.__name__}")
        print(f"   Custom handler: {work_updater.__class__.__name__}")
        
        return True
        
    except Exception as e:
        import traceback
        logger.error(f"Manual chain composition test failed: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False


def test_backward_compatibility():
    """Test backward compatibility with old factories."""
    print("\n" + "=" * 60)
    print("Testing Backward Compatibility")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config()
        
        # Test that old factory imports still work
        from core.handlers import DataFetcherFactory, WorkUpdaterFactory, DataSyncFactory, NotificationFactory
        
        # These should still work (though not recommended)
        old_data_fetcher = DataFetcherFactory.create_fetcher(config, logger)
        old_work_updater = WorkUpdaterFactory.create_updater(config, logger)
        old_data_sync = DataSyncFactory.create_sync_handler(config, logger)
        old_notification = NotificationFactory.create_notification_handler(config, logger)
        
        assert old_data_fetcher is not None
        assert old_work_updater is not None
        assert old_data_sync is not None
        assert old_notification is not None
        
        print("✅ Backward compatibility test passed")
        print("   Old factories still work for existing code")
        print("   But new simplified approach is recommended")
        
        return True
        
    except Exception as e:
        logger.error(f"Backward compatibility test failed: {e}")
        return False


def main():
    """Main test function."""
    print("=" * 60)
    print("Testing Simplified Handler Usage")
    print("=" * 60)
    
    tests = [
        test_direct_inheritance,
        test_convenience_functions,
        test_core_factory,
        test_default_chain_builder,
        test_client_specific_chain,
        test_chain_composition,
        test_backward_compatibility
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("TEST RESULTS")
    print("=" * 60)
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total: {passed + failed}")
    
    if failed == 0:
        print("✅ All tests passed!")
        print("\n🎉 重构成功！新的简化方式工作正常")
        print("\n📚 推荐使用方式:")
        print("   1. 直接继承核心处理器")
        print("   2. 使用便利函数快速创建")
        print("   3. 手动组合构建自定义链")
        return True
    else:
        print("❌ Some tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
