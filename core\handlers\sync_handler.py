"""
同步数据Handler - 负责将更新后的数据同步回数据源
"""

import logging
import time
from typing import List, Dict, Any
from abc import ABC, abstractmethod

from .base_handler import BaseHandler
from ..context import WorkContext, ClientConfig
from ..exceptions import DataSyncException
from models.work_models import WorkDetailTaskModel


class SyncHandler(BaseHandler):
    """
    数据同步处理器基类
    
    职责：
    - 将更新后的作品数据同步回数据源
    - 应用字段映射规则
    - 处理批量同步逻辑
    """
    
    def __init__(self, client_config: ClientConfig, logger: logging.Logger):
        super().__init__(client_config, logger)
        self.logger = logger.getChild(self.__class__.__name__)
    
    def handle(self, context: WorkContext) -> WorkContext:
        """
        处理数据同步逻辑
        
        Args:
            context: 工作上下文
            
        Returns:
            更新后的工作上下文
        """
        try:
            self.logger.info(f"Starting data sync for {len(context.updated_works)} works")
            
            # 批量同步数据
            sync_results = self._sync_data_batch(context.updated_works, context)
            
            # 设置到上下文
            context.sync_results = sync_results
            context.successful_syncs = len([r for r in sync_results if r.get('success', False)])
            context.failed_syncs = len(sync_results) - context.successful_syncs
            
            self.logger.info(f"Data sync completed: {context.successful_syncs} successful, {context.failed_syncs} failed")
            
            # 传递给下一个处理器
            return super().handle(context)
            
        except Exception as e:
            self.logger.error(f"Data sync process failed: {e}")
            raise DataSyncException(f"Failed to sync data: {str(e)}")
    
    def _sync_data_batch(self, works: List[WorkDetailTaskModel], 
                        context: WorkContext) -> List[Dict[str, Any]]:
        """
        批量同步数据
        
        Args:
            works: 待同步的作品列表
            context: 工作上下文
            
        Returns:
            同步结果列表
        """
        sync_results = []
        batch_size = self._get_batch_size()
        
        for i in range(0, len(works), batch_size):
            batch = works[i:i + batch_size]
            self.logger.info(f"Syncing batch {i//batch_size + 1}: {len(batch)} works")
            
            for work in batch:
                try:
                    result = self._sync_single_work(work, context)
                    sync_results.append(result)
                    
                    # 添加延迟避免API限制
                    time.sleep(0.05)
                    
                except Exception as e:
                    self.logger.error(f"Failed to sync work {work.work_id}: {e}")
                    sync_results.append({
                        'work_id': work.work_id,
                        'success': False,
                        'error': str(e)
                    })
        
        return sync_results
    
    def _sync_single_work(self, work: WorkDetailTaskModel, 
                         context: WorkContext) -> Dict[str, Any]:
        """
        同步单个作品
        
        Args:
            work: 待同步的作品
            context: 工作上下文
            
        Returns:
            同步结果
        """
        try:
            self.logger.debug(f"Syncing work: {work.work_id}")
            
            # 应用字段映射
            mapped_data = self._apply_field_mapping(work)
            
            # 执行同步
            sync_success = self._perform_sync(work, mapped_data, context)
            
            return {
                'work_id': work.work_id,
                'success': sync_success,
                'mapped_fields': len(mapped_data),
                'sync_time': time.time()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to sync single work {work.work_id}: {e}")
            return {
                'work_id': work.work_id,
                'success': False,
                'error': str(e)
            }
    
    def _apply_field_mapping(self, work: WorkDetailTaskModel) -> Dict[str, Any]:
        """
        应用字段映射
        
        Args:
            work: 作品信息
            
        Returns:
            映射后的字段数据
        """
        try:
            field_mapping = self._get_field_mapping()
            mapped_data = {}
            
            # 基本字段映射
            if work.author_work:
                author_work = work.author_work
                
                for target_field, source_field in field_mapping.items():
                    try:
                        if source_field == 'title':
                            mapped_data[target_field] = getattr(author_work, 'title', '')
                        elif source_field == 'author_name':
                            mapped_data[target_field] = getattr(author_work, 'author_name', '')
                        elif source_field == 'author_url':
                            mapped_data[target_field] = getattr(author_work, 'author_url', '')
                        elif source_field == 'content':
                            mapped_data[target_field] = getattr(author_work, 'content', '')
                        elif source_field == 'publish_time':
                            mapped_data[target_field] = getattr(author_work, 'publish_time', '')
                        elif source_field == 'like_count':
                            mapped_data[target_field] = getattr(author_work, 'like_count', 0)
                        elif source_field == 'comment_count':
                            mapped_data[target_field] = getattr(author_work, 'comment_count', 0)
                        elif source_field == 'share_count':
                            mapped_data[target_field] = getattr(author_work, 'share_count', 0)
                        elif source_field == 'collect_count':
                            mapped_data[target_field] = getattr(author_work, 'collect_count', 0)
                    except Exception as e:
                        self.logger.warning(f"Failed to map field {target_field}: {e}")
            
            # 系统字段映射
            if 'platform_name' in field_mapping.values():
                platform_key = next(k for k, v in field_mapping.items() if v == 'platform_name')
                mapped_data[platform_key] = self._get_platform_name(work.platform)
            
            if 'update_count' in field_mapping.values():
                update_key = next(k for k, v in field_mapping.items() if v == 'update_count')
                mapped_data[update_key] = work.update_count
            
            if 'threshold' in field_mapping.values():
                threshold_key = next(k for k, v in field_mapping.items() if v == 'threshold')
                mapped_data[threshold_key] = work.threshold
            
            if 'record_time' in field_mapping.values():
                time_key = next(k for k, v in field_mapping.items() if v == 'record_time')
                mapped_data[time_key] = time.strftime('%Y-%m-%d %H:%M:%S')
            
            return mapped_data
            
        except Exception as e:
            self.logger.error(f"Failed to apply field mapping: {e}")
            return {}
    
    def _get_field_mapping(self) -> Dict[str, str]:
        """获取字段映射配置"""
        data_sync_config = self.client_config.data_sync_config or {}
        return data_sync_config.get('field_mapping', {})
    
    def _get_platform_name(self, platform: str) -> str:
        """获取平台显示名称"""
        platform_names = {
            'xhs': '小红书',
            'dy': '抖音',
            'ks': '快手',
            'wxvideo': '微信视频号'
        }
        return platform_names.get(platform, platform)
    
    def _get_batch_size(self) -> int:
        """获取批处理大小"""
        data_sync_config = self.client_config.data_sync_config or {}
        return data_sync_config.get('batch_size', 20)
    
    @abstractmethod
    def _perform_sync(self, work: WorkDetailTaskModel, 
                     mapped_data: Dict[str, Any], 
                     context: WorkContext) -> bool:
        """
        执行同步操作 - 子类必须实现
        
        Args:
            work: 作品信息
            mapped_data: 映射后的数据
            context: 工作上下文
            
        Returns:
            是否同步成功
        """
        pass


class DingTalkSyncHandler(SyncHandler):
    """
    钉钉多维表格同步处理器
    """
    
    def __init__(self, client_config: ClientConfig, logger: logging.Logger):
        super().__init__(client_config, logger)
        self._bitable_client = None
    
    def _perform_sync(self, work: WorkDetailTaskModel, 
                     mapped_data: Dict[str, Any], 
                     context: WorkContext) -> bool:
        """执行钉钉同步"""
        try:
            bitable_client = self._get_bitable_client()
            
            # 更新记录
            result = bitable_client.update_record(work.row_id, mapped_data)
            
            if result:
                self.logger.debug(f"Successfully synced work {work.work_id} to DingTalk")
                return True
            else:
                self.logger.warning(f"Failed to sync work {work.work_id} to DingTalk")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to sync to DingTalk: {e}")
            return False
    
    def _get_bitable_client(self):
        """获取钉钉多维表格客户端"""
        if self._bitable_client is None:
            try:
                from jss_api_extend.dingtalk_bitable_factory import DingtalkBitableFactory
                
                bitable_config = self.client_config.bitable_config
                if not bitable_config:
                    raise DataSyncException("Missing bitable configuration")
                
                self._bitable_client = DingtalkBitableFactory.create_bitable_client(
                    api_token=self.client_config.user_info.get('api-token'),
                    dentry_uuid=bitable_config.get('dentryUuid'),
                    id_or_name=bitable_config.get('idOrName')
                )
                
            except ImportError as e:
                self.logger.error(f"Failed to import DingTalk dependencies: {e}")
                raise DataSyncException("DingTalk dependencies not available")
            except Exception as e:
                self.logger.error(f"Failed to create DingTalk client: {e}")
                raise DataSyncException(f"DingTalk client creation failed: {str(e)}")
        
        return self._bitable_client


class WebhookSyncHandler(SyncHandler):
    """
    Webhook同步处理器
    """
    
    def __init__(self, client_config: ClientConfig, logger: logging.Logger):
        super().__init__(client_config, logger)
    
    def _perform_sync(self, work: WorkDetailTaskModel, 
                     mapped_data: Dict[str, Any], 
                     context: WorkContext) -> bool:
        """执行Webhook同步"""
        try:
            webhook_url = self._get_webhook_url()
            if not webhook_url:
                self.logger.warning("No webhook URL configured")
                return False
            
            # 构建webhook数据
            webhook_data = {
                'work_id': work.work_id,
                'work_url': work.work_url,
                'platform': work.platform,
                'row_id': work.row_id,
                'batch_id': context.batch_id,
                'client_name': context.client_name,
                'mapped_data': mapped_data,
                'timestamp': time.time()
            }
            
            # 发送webhook请求
            import requests
            response = requests.post(
                webhook_url,
                json=webhook_data,
                timeout=30,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                self.logger.debug(f"Successfully sent webhook for work {work.work_id}")
                return True
            else:
                self.logger.warning(f"Webhook returned status {response.status_code} for work {work.work_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to send webhook: {e}")
            return False
    
    def _get_webhook_url(self) -> str:
        """获取webhook URL"""
        bitable_config = self.client_config.bitable_config or {}
        return bitable_config.get('webhook', '')
