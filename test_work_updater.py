#!/usr/bin/env python3
"""
Test script for work updater handlers.
"""

import logging
import sys
from datetime import datetime
from unittest.mock import Mock, MagicMock, AsyncMock

# Add current directory to path for imports
sys.path.insert(0, '.')

from core.context import WorkContext, ClientConfig
from core.handlers.work_updater_factory import create_work_updater
from core.handlers.work_updater_handler import WorkUpdaterHandler
from core.handlers.database_work_updater import DatabaseWorkUpdater
from models.work_models import WorkDetailTaskModel
from core.exceptions import WorkUpdateException


def setup_test_logging():
    """Setup logging for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger('test_work_updater')


def create_test_config():
    """Create a test client configuration."""
    config_data = {
        'name': 'test-client',
        'work_updater': {
            'platforms': ['xhs', 'dy'],
            'batch_size': 2
        },
        'user_info': {
            'api-token': 'test-token',
            'user_id': 123
        }
    }
    
    return ClientConfig.from_dict(config_data)


def create_test_tasks():
    """Create test tasks for processing."""
    return [
        WorkDetailTaskModel(
            record_id='test-batch-001',
            work_url='https://www.xiaohongshu.com/discovery/item/686e6a68000000002203d213',
            work_id='686e6a68000000002203d213',
            platform='xhs',
            row_id='row1',
            update_count=1
        ),
        WorkDetailTaskModel(
            record_id='test-batch-001',
            work_url='https://www.douyin.com/video/1234567890123456789',
            work_id='1234567890123456789',
            platform='dy',
            row_id='row2',
            update_count=2
        ),
        WorkDetailTaskModel(
            record_id='test-batch-001',
            work_url='https://www.kuaishou.com/video/test123',
            work_id='test123',
            platform='ks',  # Not in supported platforms
            row_id='row3',
            update_count=1
        )
    ]


def test_work_updater_factory():
    """Test the work updater factory."""
    print("=" * 60)
    print("Testing Work Updater Factory")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config()
        
        # Test basic updater creation
        basic_updater = create_work_updater(config, logger, 'basic')
        assert isinstance(basic_updater, WorkUpdaterHandler), f"Expected WorkUpdaterHandler, got {type(basic_updater)}"
        print("✅ Basic work updater created successfully")
        
        # Test database updater creation
        db_updater = create_work_updater(config, logger, 'database')
        assert isinstance(db_updater, DatabaseWorkUpdater), f"Expected DatabaseWorkUpdater, got {type(db_updater)}"
        print("✅ Database work updater created successfully")
        
        # Test default updater creation
        default_updater = create_work_updater(config, logger)
        assert isinstance(default_updater, DatabaseWorkUpdater), f"Expected DatabaseWorkUpdater as default, got {type(default_updater)}"
        print("✅ Default work updater created successfully")
        
        # Test unsupported updater type
        try:
            unsupported_updater = create_work_updater(config, logger, 'unsupported_type')
            assert False, "Should have raised exception for unsupported type"
        except Exception as e:
            print(f"✅ Correctly raised exception for unsupported type: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"Factory test failed: {e}")
        return False


def test_work_updater_with_mocks():
    """Test work updater with mocked platform handlers."""
    print("\n" + "=" * 60)
    print("Testing Work Updater with Mocked Handlers")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config()
        updater = create_work_updater(config, logger, 'basic')
        
        # Mock platform handlers
        mock_xhs_handler = Mock()
        mock_dy_handler = AsyncMock()
        
        # Create mock work details
        mock_xhs_work = Mock()
        mock_xhs_work.title = "Test XHS Title"
        mock_xhs_work.like_count = 100
        mock_xhs_work.comment_count = 20
        mock_xhs_work.author_name = "Test XHS Author"
        mock_xhs_work.work_id = "686e6a68000000002203d213"
        
        mock_dy_work = Mock()
        mock_dy_work.title = "Test DY Title"
        mock_dy_work.like_count = 200
        mock_dy_work.comment_count = 50
        mock_dy_work.author_name = "Test DY Author"
        mock_dy_work.work_id = "1234567890123456789"
        
        # Configure mock handlers
        mock_xhs_handler.query_article_detail.return_value = mock_xhs_work
        mock_dy_handler.query_article_detail.return_value = mock_dy_work
        
        # Replace platform handlers
        updater._platform_handlers = {
            'xhs': mock_xhs_handler,
            'dy': mock_dy_handler
        }
        
        # Create test context
        context = WorkContext(
            client_name='test-client',
            batch_id='test-batch-001',
            client_config=config
        )
        
        # Set formatted tasks
        context.formatted_tasks = create_test_tasks()
        
        # Process tasks
        result = updater.handle(context)
        
        assert result == True, "Handler should return True"
        assert len(context.updated_works) == 2, f"Expected 2 updated works, got {len(context.updated_works)}"
        assert context.successful_updates == 2, f"Expected 2 successful updates, got {context.successful_updates}"
        assert context.failed_updates == 0, f"Expected 0 failed updates, got {context.failed_updates}"
        
        # Check that unsupported platform was filtered out
        platforms = [task.platform for task in context.updated_works]
        assert 'ks' not in platforms, "Unsupported platform should be filtered out"
        assert 'xhs' in platforms, "XHS platform should be included"
        assert 'dy' in platforms, "DY platform should be included"
        
        print(f"✅ Successfully processed {len(context.updated_works)} tasks")
        print(f"   Successful updates: {context.successful_updates}")
        print(f"   Failed updates: {context.failed_updates}")
        print(f"   Platforms processed: {platforms}")
        
        return True
        
    except Exception as e:
        logger.error(f"Work updater test failed: {e}")
        return False


def test_platform_filtering():
    """Test platform filtering logic."""
    print("\n" + "=" * 60)
    print("Testing Platform Filtering")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config()
        updater = create_work_updater(config, logger, 'basic')
        
        # Test tasks with different platforms
        test_tasks = create_test_tasks()
        
        # Filter by supported platforms
        supported_platforms = ['xhs', 'dy']
        filtered_tasks = updater._filter_tasks_by_platform(test_tasks, supported_platforms)
        
        assert len(filtered_tasks) == 2, f"Expected 2 filtered tasks, got {len(filtered_tasks)}"
        
        platforms = [task.platform for task in filtered_tasks]
        assert 'xhs' in platforms, "XHS should be included"
        assert 'dy' in platforms, "DY should be included"
        assert 'ks' not in platforms, "KS should be filtered out"
        
        print(f"✅ Platform filtering test passed")
        print(f"   Original tasks: {len(test_tasks)}")
        print(f"   Filtered tasks: {len(filtered_tasks)}")
        print(f"   Included platforms: {platforms}")
        
        return True
        
    except Exception as e:
        logger.error(f"Platform filtering test failed: {e}")
        return False


def test_error_handling():
    """Test error handling in work updater."""
    print("\n" + "=" * 60)
    print("Testing Error Handling")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config()
        updater = create_work_updater(config, logger, 'basic')
        
        # Mock platform handler that fails
        mock_handler = Mock()
        mock_handler.query_article_detail.side_effect = Exception("Mock platform error")
        
        updater._platform_handlers = {
            'xhs': mock_handler,
            'dy': mock_handler
        }
        
        # Create test context
        context = WorkContext(
            client_name='test-client',
            batch_id='test-batch-error',
            client_config=config
        )
        
        # Set formatted tasks
        context.formatted_tasks = create_test_tasks()[:1]  # Only XHS task
        
        # Process tasks (should handle errors gracefully)
        result = updater.handle(context)
        
        assert result == True, "Handler should return True even with errors"
        assert len(context.updated_works) == 0, f"Expected 0 updated works, got {len(context.updated_works)}"
        assert context.successful_updates == 0, f"Expected 0 successful updates, got {context.successful_updates}"
        assert context.failed_updates == 1, f"Expected 1 failed update, got {context.failed_updates}"
        assert len(context.error_messages) > 0, "Should have error messages"
        
        print(f"✅ Error handling test passed")
        print(f"   Failed updates: {context.failed_updates}")
        print(f"   Error messages: {len(context.error_messages)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error handling test failed: {e}")
        return False


def test_batch_processing():
    """Test batch processing logic."""
    print("\n" + "=" * 60)
    print("Testing Batch Processing")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        # Create config with small batch size
        config_data = {
            'name': 'test-client',
            'work_updater': {
                'platforms': ['xhs'],
                'batch_size': 1  # Process one at a time
            }
        }
        config = ClientConfig.from_dict(config_data)
        
        updater = create_work_updater(config, logger, 'basic')
        
        # Mock platform handler
        mock_handler = Mock()
        mock_work = Mock()
        mock_work.title = "Test Title"
        mock_work.like_count = 100
        mock_handler.query_article_detail.return_value = mock_work
        
        updater._platform_handlers = {'xhs': mock_handler}
        
        # Create multiple XHS tasks
        tasks = [
            WorkDetailTaskModel(
                record_id='test-batch-001',
                work_url=f'https://www.xiaohongshu.com/discovery/item/686e6a6800000000220{i:05d}',
                work_id=f'686e6a6800000000220{i:05d}',
                platform='xhs',
                row_id=f'row{i}',
                update_count=1
            ) for i in range(3)
        ]
        
        context = WorkContext(
            client_name='test-client',
            batch_id='test-batch-001',
            client_config=config
        )
        context.formatted_tasks = tasks
        
        # Process tasks
        result = updater.handle(context)
        
        assert result == True, "Handler should return True"
        assert len(context.updated_works) == 3, f"Expected 3 updated works, got {len(context.updated_works)}"
        assert context.successful_updates == 3, f"Expected 3 successful updates, got {context.successful_updates}"
        
        # Verify that handler was called for each task
        assert mock_handler.query_article_detail.call_count == 3, f"Expected 3 handler calls, got {mock_handler.query_article_detail.call_count}"
        
        print(f"✅ Batch processing test passed")
        print(f"   Tasks processed: {len(context.updated_works)}")
        print(f"   Handler calls: {mock_handler.query_article_detail.call_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"Batch processing test failed: {e}")
        return False


def main():
    """Main test function."""
    print("=" * 60)
    print("Testing Work Updater Handlers")
    print("=" * 60)
    
    tests = [
        test_work_updater_factory,
        test_work_updater_with_mocks,
        test_platform_filtering,
        test_error_handling,
        test_batch_processing
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("TEST RESULTS")
    print("=" * 60)
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total: {passed + failed}")
    
    if failed == 0:
        print("✅ All tests passed!")
        return True
    else:
        print("❌ Some tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
