#!/usr/bin/env python3
"""
Integration test for notifications in the complete chain of responsibility.
"""

import logging
import sys
from datetime import datetime
from unittest.mock import Mock, MagicMock, AsyncMock

# Add current directory to path for imports
sys.path.insert(0, '.')

from core.context import WorkContext, ClientConfig
from core.handlers.data_fetcher_factory import create_data_fetcher
from core.handlers.work_updater_factory import create_work_updater
from core.handlers.data_sync_factory import create_data_sync_handler
from core.handlers.notification_factory import create_notification_handler
from core.chain_executor import ChainExecutor
from models.work_models import WorkDetailTaskModel
from core.exceptions import NotificationException


def setup_test_logging():
    """Setup logging for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger('test_integration')


def create_test_config():
    """Create a test client configuration."""
    config_data = {
        'name': 'test-client',
        'data_fetcher': {
            'source_type': 'dingtalk_bitable',
            'filter_rules': {
                'update_times_limit': 5
            }
        },
        'work_updater': {
            'platforms': ['xhs', 'dy'],
            'batch_size': 2
        },
        'data_sync': {
            'field_mapping': {
                '作品标题': 'title',
                '平台': 'platform_name',
                '作者': 'author_name',
                '点赞数': 'like_count',
                '评论数': 'comment_count',
                '更新次数': 'update_count',
                '是否触发阈值': 'threshold',
                '更新时间': 'record_time'
            },
            'batch_size': 10
        },
        'notification': {
            'enabled_channels': ['dingtalk'],
            'threshold_rules': {
                'test-client': {
                    'like_count': 100,
                    'comment_count': 30
                },
                'default': {
                    'like_count': 200,
                    'comment_count': 50
                }
            },
            'message_templates': {
                'test-client': '🚨 作品阈值通知：标题={title}, 作者={author_name}, 平台={platform}, {threshold_info}, 作品链接={work_url}',
                'default': '作品阈值通知：{title} - {author_name} ({platform})'
            },
            'default_recipients': [
                {'unionId': 'admin-user-1'},
                {'unionId': 'admin-user-2'}
            ]
        },
        'user_info': {
            'api-token': 'test-token',
            'user_id': 123,
            'tenant': 'test-tenant'
        },
        'bitable': {
            'dentryUuid': 'test-dentry-uuid',
            'idOrName': 'test-table',
            'webhook': 'https://test-webhook.com/sync'
        }
    }
    
    return ClientConfig.from_dict(config_data)


def test_complete_chain_with_notifications():
    """Test complete chain including notifications."""
    print("=" * 60)
    print("Testing Complete Chain with Notifications")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        # Create test configuration
        config = create_test_config()
        
        # Create handlers
        data_fetcher = create_data_fetcher(config, logger)
        work_updater = create_work_updater(config, logger, 'basic')  # Use basic to avoid DB dependencies
        data_sync = create_data_sync_handler(config, logger, 'webhook')  # Use webhook for testing

        # Create a fresh notification handler to avoid state issues
        from core.handlers.dingtalk_notification import DingTalkNotification
        notification_handler = DingTalkNotification(config, logger)
        
        # Mock the bitable client for data fetcher
        mock_bitable_client = Mock()
        mock_records = [
            Mock(
                work_url='https://www.xiaohongshu.com/discovery/item/686e6a68000000002203d213',
                update_count=2,
                user_union_id='test-user',
                row_id='row123',
                extends={}
            ),
            Mock(
                work_url='https://www.xiaohongshu.com/discovery/item/686e6a68000000002203d214',
                update_count=1,
                user_union_id='test-user2',
                row_id='row456',
                extends={}
            )
        ]
        
        mock_bitable_client.list_bitable_data_by_api_filter.return_value = mock_records
        data_fetcher._bitable_client = mock_bitable_client
        
        # Mock platform handlers for work updater
        mock_xhs_handler = Mock()
        
        # Create mock work details with different engagement levels
        mock_high_engagement_work = Mock()
        mock_high_engagement_work.title = "High Engagement XHS Work"
        mock_high_engagement_work.content = "This work has high engagement"
        mock_high_engagement_work.author_name = "Popular XHS Author"
        mock_high_engagement_work.author_url = "https://www.xiaohongshu.com/user/profile/popular"
        mock_high_engagement_work.publish_time = "2024-01-01 12:00:00"
        mock_high_engagement_work.like_count = 150  # Above threshold (100)
        mock_high_engagement_work.comment_count = 50  # Above threshold (30)
        mock_high_engagement_work.share_count = 10
        mock_high_engagement_work.collect_count = 25
        mock_high_engagement_work.work_id = "686e6a68000000002203d213"
        
        mock_low_engagement_work = Mock()
        mock_low_engagement_work.title = "Low Engagement XHS Work"
        mock_low_engagement_work.content = "This work has low engagement"
        mock_low_engagement_work.author_name = "Regular XHS Author"
        mock_low_engagement_work.author_url = "https://www.xiaohongshu.com/user/profile/regular"
        mock_low_engagement_work.publish_time = "2024-01-02 15:30:00"
        mock_low_engagement_work.like_count = 50  # Below threshold (100)
        mock_low_engagement_work.comment_count = 20  # Below threshold (30)
        mock_low_engagement_work.share_count = 3
        mock_low_engagement_work.collect_count = 8
        mock_low_engagement_work.work_id = "686e6a68000000002203d214"
        
        # Configure mock handler to return different works based on work_id
        def mock_query_detail(work_id):
            if work_id == "686e6a68000000002203d213":
                return mock_high_engagement_work
            else:
                return mock_low_engagement_work
        
        mock_xhs_handler.query_article_detail.side_effect = mock_query_detail
        
        # Replace platform handlers
        work_updater._platform_handlers = {
            'xhs': mock_xhs_handler
        }
        
        # Mock data sync client
        mock_sync_client = Mock()
        mock_sync_client.constom_invoke.return_value = True
        data_sync._data_sync_client = mock_sync_client
        
        # Mock DingTalk notification client
        mock_dingtalk_client = Mock()
        mock_dingtalk_client.send_message.return_value = True
        notification_handler._dingtalk_client = mock_dingtalk_client

        # Enable test mode to avoid "already_notified" issues
        notification_handler._test_mode = True
        
        # Chain handlers together
        data_fetcher.set_next(work_updater).set_next(data_sync).set_next(notification_handler)

        # Create chain executor
        executor = ChainExecutor(config, logger)
        executor.set_chain(data_fetcher)
        
        # Execute the complete chain
        context = executor.execute("test-batch-integration")
        
        # Verify results at each stage
        assert len(context.raw_tasks) == 2, f"Expected 2 raw tasks, got {len(context.raw_tasks)}"
        assert len(context.formatted_tasks) == 2, f"Expected 2 formatted tasks, got {len(context.formatted_tasks)}"
        assert len(context.updated_works) == 2, f"Expected 2 updated works, got {len(context.updated_works)}"
        assert len(context.synced_works) == 2, f"Expected 2 synced works, got {len(context.synced_works)}"
        assert len(context.notification_results) == 2, f"Expected 2 notification results, got {len(context.notification_results)}"
        
        # Check notification results
        assert context.notifications_sent == 1, f"Expected 1 notification sent, got {context.notifications_sent}"
        
        # Verify that only high engagement work triggered notification
        high_engagement_result = next((r for r in context.notification_results if r['work_id'] == "686e6a68000000002203d213"), None)
        low_engagement_result = next((r for r in context.notification_results if r['work_id'] == "686e6a68000000002203d214"), None)
        
        assert high_engagement_result is not None, "Should have result for high engagement work"
        assert low_engagement_result is not None, "Should have result for low engagement work"
        
        assert high_engagement_result['success'] == True, "High engagement notification should succeed"
        assert high_engagement_result['sent'] == True, "High engagement notification should be sent"
        
        assert low_engagement_result['success'] == True, "Low engagement processing should succeed"
        assert low_engagement_result['sent'] == False, "Low engagement notification should not be sent"
        assert low_engagement_result['reason'] == 'threshold_not_met', "Should indicate threshold not met"
        
        # Verify that DingTalk client was called for high engagement work
        assert mock_dingtalk_client.send_message.called, "DingTalk client should have been called"
        
        # Check that threshold was updated for high engagement work
        high_engagement_work = next((w for w in context.synced_works if w.work_id == "686e6a68000000002203d213"), None)
        assert high_engagement_work is not None, "Should find high engagement work"
        assert high_engagement_work.threshold == "是", "High engagement work threshold should be updated"
        
        print("✅ Complete chain with notifications test passed")
        print(f"   Raw tasks: {len(context.raw_tasks)}")
        print(f"   Formatted tasks: {len(context.formatted_tasks)}")
        print(f"   Updated works: {len(context.updated_works)}")
        print(f"   Synced works: {len(context.synced_works)}")
        print(f"   Notification results: {len(context.notification_results)}")
        print(f"   Notifications sent: {context.notifications_sent}")
        print(f"   High engagement sent: {high_engagement_result['sent']}")
        print(f"   Low engagement reason: {low_engagement_result['reason']}")
        print(f"   DingTalk calls: {mock_dingtalk_client.send_message.call_count}")
        
        # Check execution summary
        summary = context.to_summary()
        print(f"\nExecution Summary:")
        print(f"   Execution time: {summary['execution_time']:.3f}s")
        print(f"   Success rate: {summary['success_rate']:.1f}%")
        print(f"   Total processed: {summary['total_processed']}")
        print(f"   Successful updates: {summary['successful_updates']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Complete chain with notifications test failed: {e}")
        return False


def test_notification_message_content():
    """Test notification message content and formatting."""
    print("\n" + "=" * 60)
    print("Testing Notification Message Content")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config()
        notification_handler = create_notification_handler(config, logger, 'dingtalk')
        
        # Mock DingTalk client to capture messages
        mock_dingtalk_client = Mock()
        sent_messages = []
        
        def capture_message(union_id, message):
            sent_messages.append({'union_id': union_id, 'message': message})
            return True
        
        mock_dingtalk_client.send_message.side_effect = capture_message
        notification_handler._dingtalk_client = mock_dingtalk_client
        notification_handler._test_mode = True
        
        # Create test context with high engagement work
        context = WorkContext(
            client_name='test-client',
            batch_id='test-batch-message',
            client_config=config
        )
        
        # Create mock work with detailed data
        mock_work = Mock()
        mock_work.title = "Amazing Product Review"
        mock_work.content = "Detailed review of an amazing product"
        mock_work.author_name = "Influencer Author"
        mock_work.author_url = "https://www.xiaohongshu.com/user/profile/influencer"
        mock_work.publish_time = "2024-01-15 10:30:00"
        mock_work.like_count = 500  # Well above threshold
        mock_work.comment_count = 100  # Well above threshold
        mock_work.share_count = 25
        mock_work.collect_count = 75
        
        context.synced_works = [
            WorkDetailTaskModel(
                record_id='test-batch-message',
                work_url='https://www.xiaohongshu.com/discovery/item/amazing-review',
                work_id='amazing-review',
                platform='xhs',
                row_id='message-row',
                dentry_uuid='test-dentry-uuid',
                id_or_name='test-table',
                update_count=3,
                threshold='否',
                author_work=mock_work,
                submit_user=[
                    {'unionId': 'content-user-1', 'name': 'Content Manager 1'}
                ]
            )
        ]
        
        # Process notifications
        result = notification_handler.handle(context)
        
        assert result == True, "Notification processing should succeed"
        assert context.notifications_sent == 1, f"Expected 1 notification sent, got {context.notifications_sent}"
        assert len(sent_messages) > 0, "Should have captured sent messages"
        
        # Check message content
        message = sent_messages[0]['message']
        
        # Verify message contains expected elements
        assert "Amazing Product Review" in message, "Message should contain work title"
        assert "Influencer Author" in message, "Message should contain author name"
        assert "小红书" in message, "Message should contain platform name"
        assert "点赞数=500" in message, "Message should contain like count with threshold info"
        assert "评论数=100" in message, "Message should contain comment count with threshold info"
        assert "https://www.xiaohongshu.com/discovery/item/amazing-review" in message, "Message should contain work URL"
        
        print("✅ Notification message content test passed")
        print(f"   Messages sent: {len(sent_messages)}")
        print(f"   Message length: {len(message)}")
        print(f"   Message preview: {message[:150]}...")
        
        return True
        
    except Exception as e:
        logger.error(f"Message content test failed: {e}")
        return False


def test_multi_recipient_notifications():
    """Test notifications to multiple recipients."""
    print("\n" + "=" * 60)
    print("Testing Multi-Recipient Notifications")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config()
        notification_handler = create_notification_handler(config, logger, 'dingtalk')
        
        # Mock DingTalk client to track recipients
        mock_dingtalk_client = Mock()
        sent_to_recipients = []
        
        def track_recipient(union_id, message):
            sent_to_recipients.append(union_id)
            return True
        
        mock_dingtalk_client.send_message.side_effect = track_recipient
        notification_handler._dingtalk_client = mock_dingtalk_client
        notification_handler._test_mode = True
        
        # Create test context
        context = WorkContext(
            client_name='test-client',
            batch_id='test-batch-multi',
            client_config=config
        )
        
        # Create mock work with multiple submit users
        mock_work = Mock()
        mock_work.title = "Multi-Recipient Work"
        mock_work.author_name = "Multi Author"
        mock_work.like_count = 300  # Above threshold
        mock_work.comment_count = 80  # Above threshold
        
        context.synced_works = [
            WorkDetailTaskModel(
                record_id='test-batch-multi',
                work_url='https://www.xiaohongshu.com/discovery/item/multi-recipient',
                work_id='multi-recipient',
                platform='xhs',
                row_id='multi-row',
                update_count=1,
                threshold='否',
                author_work=mock_work,
                submit_user=[
                    {'unionId': 'user-1', 'name': 'User 1'},
                    {'unionId': 'user-2', 'name': 'User 2'},
                    {'unionId': 'user-3', 'name': 'User 3'}
                ]
            )
        ]
        
        # Process notifications
        result = notification_handler.handle(context)
        
        assert result == True, "Notification processing should succeed"
        assert context.notifications_sent == 1, f"Expected 1 notification sent, got {context.notifications_sent}"
        
        # Check that all recipients received notifications
        # Should include submit_user + default_recipients (with duplicates removed)
        expected_recipients = {'user-1', 'user-2', 'user-3', 'admin-user-1', 'admin-user-2'}
        actual_recipients = set(sent_to_recipients)
        
        assert len(actual_recipients) >= 3, f"Expected at least 3 recipients, got {len(actual_recipients)}"
        assert 'user-1' in actual_recipients, "Should send to user-1"
        assert 'user-2' in actual_recipients, "Should send to user-2"
        assert 'user-3' in actual_recipients, "Should send to user-3"
        
        print("✅ Multi-recipient notifications test passed")
        print(f"   Total recipients: {len(actual_recipients)}")
        print(f"   Recipients: {sorted(actual_recipients)}")
        print(f"   DingTalk calls: {mock_dingtalk_client.send_message.call_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"Multi-recipient test failed: {e}")
        return False


def main():
    """Main test function."""
    print("=" * 60)
    print("Notification Integration Tests")
    print("=" * 60)
    
    tests = [
        test_complete_chain_with_notifications,
        test_notification_message_content,
        test_multi_recipient_notifications
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("INTEGRATION TEST RESULTS")
    print("=" * 60)
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total: {passed + failed}")
    
    if failed == 0:
        print("✅ All integration tests passed!")
        return True
    else:
        print("❌ Some integration tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
