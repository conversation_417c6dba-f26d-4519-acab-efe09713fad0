"""
Pipeline上下文 - 用于在责任链中传递数据和状态
"""

import time
from dataclasses import dataclass, field
from typing import List, Any

from models.work_models import WorkDetailTaskModel
from .context import ClientConfig


@dataclass
class PipelineContext:
    # 基本信息
    client_name: str
    # 每次运行的 batch_id
    batch_id: str

    client_config: ClientConfig
    start_time: float = field(default_factory=time.time)
    data_source_type: str = "bitable"

    # 错误 url 格式链接
    error_format_url_list: List[Any] = field(default_factory=list)

    # 任务列表
    task_list: List[WorkDetailTaskModel] = field(default_factory=list)

    # 更新逻辑阶段 (UpdateHandler)
    updated_works: List[WorkDetailTaskModel] = field(default_factory=list)

    # 成功更新条数
    successful_updates: int = 0
    # 成功更新条数
    failed_updates: int = 0

    # 更新失败的任务(可能是作品不存在，可能是API错误)
    update_errors: List[WorkDetailTaskModel] = field(default_factory=list)
