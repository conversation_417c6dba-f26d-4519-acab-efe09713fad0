#!/usr/bin/env python3
"""
Integration test for data fetcher in the chain of responsibility.
"""

import logging
import sys
from datetime import datetime
from unittest.mock import Mock, MagicMock

# Add current directory to path for imports
sys.path.insert(0, '.')

from core.context import WorkContext, ClientConfig
from core.handlers.data_fetcher_factory import create_data_fetcher
from core.chain_executor import ChainExecutor
from core.exceptions import DataFetchException


class MockWorkUpdater:
    """Mock work updater for testing."""

    def __init__(self, client_config, logger):
        self.client_config = client_config
        self.logger = logger
        self.next_handler = None
        self.handler_name = self.__class__.__name__
    
    def set_next(self, handler):
        self.next_handler = handler
        return handler
    
    def handle(self, context):
        self.logger.info("MockWorkUpdater: Processing fetched tasks")
        
        # Simulate updating work details
        for task in context.formatted_tasks:
            updated_task = task
            updated_task.author_work = {
                'title': f'Test Title for {task.platform}',
                'like_count': 100,
                'comment_count': 20
            }
            context.updated_works.append(updated_task)
        
        context.successful_updates = len(context.updated_works)
        self.logger.info(f"MockWorkUpdater: Updated {len(context.updated_works)} works")
        
        if self.next_handler:
            return self.next_handler.handle(context)
        return True


def setup_test_logging():
    """Setup logging for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger('test_integration')


def create_test_config():
    """Create a test client configuration."""
    config_data = {
        'name': 'test-client',
        'data_fetcher': {
            'source_type': 'dingtalk_bitable',
            'filter_rules': {
                'update_times_limit': 5
            }
        },
        'work_updater': {
            'platforms': ['xhs', 'dy'],
            'batch_size': 50
        },
        'user_info': {
            'api-token': 'test-token',
            'user_id': 123,
            'tenant': 'test-tenant'
        },
        'bitable': {
            'dentryUuid': 'test-dentry-uuid',
            'idOrName': 'test-table',
            'webhook': 'test-webhook'
        }
    }
    
    return ClientConfig.from_dict(config_data)


def test_data_fetcher_in_chain():
    """Test data fetcher as part of a handler chain."""
    print("=" * 60)
    print("Testing Data Fetcher in Chain of Responsibility")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        # Create test configuration
        config = create_test_config()
        
        # Create data fetcher
        data_fetcher = create_data_fetcher(config, logger)
        
        # Mock the bitable client
        mock_client = Mock()
        mock_records = [
            Mock(
                work_url='https://www.xiaohongshu.com/discovery/item/686e6a68000000002203d213',
                update_count=2,
                user_union_id='test-user',
                row_id='row123',
                extends={}
            ),
            Mock(
                work_url='https://www.douyin.com/video/1234567890123456789',
                update_count=1,
                user_union_id='test-user2',
                row_id='row456',
                extends={}
            )
        ]
        
        mock_client.list_bitable_data_by_api_filter.return_value = mock_records
        data_fetcher._bitable_client = mock_client
        
        # Create mock work updater
        work_updater = MockWorkUpdater(config, logger)
        
        # Chain them together
        data_fetcher.set_next(work_updater)
        
        # Create chain executor
        executor = ChainExecutor(config, logger)
        executor.set_chain(data_fetcher)
        
        # Execute the chain
        context = executor.execute("test-batch-integration")
        
        # Verify results
        assert len(context.raw_tasks) == 2, f"Expected 2 raw tasks, got {len(context.raw_tasks)}"
        assert len(context.formatted_tasks) == 2, f"Expected 2 formatted tasks, got {len(context.formatted_tasks)}"
        assert len(context.updated_works) == 2, f"Expected 2 updated works, got {len(context.updated_works)}"
        
        # Check task details
        xhs_task = next((t for t in context.formatted_tasks if t.platform == 'xhs'), None)
        dy_task = next((t for t in context.formatted_tasks if t.platform == 'dy'), None)
        
        assert xhs_task is not None, "XHS task not found"
        assert dy_task is not None, "DY task not found"
        
        assert xhs_task.work_id == '686e6a68000000002203d213'
        assert dy_task.work_id == '1234567890123456789'
        
        print("✅ Chain execution completed successfully")
        print(f"   Raw tasks: {len(context.raw_tasks)}")
        print(f"   Formatted tasks: {len(context.formatted_tasks)}")
        print(f"   Updated works: {len(context.updated_works)}")
        print(f"   XHS task ID: {xhs_task.work_id}")
        print(f"   DY task ID: {dy_task.work_id}")
        
        # Check execution summary
        summary = context.to_summary()
        print(f"\nExecution Summary:")
        print(f"   Execution time: {summary['execution_time']:.3f}s")
        print(f"   Success rate: {summary['success_rate']:.1f}%")
        print(f"   Total processed: {summary['total_processed']}")
        print(f"   Successful updates: {summary['successful_updates']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Integration test failed: {e}")
        return False


def test_error_handling():
    """Test error handling in data fetcher."""
    print("\n" + "=" * 60)
    print("Testing Error Handling")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        # Create test configuration with invalid source type
        config_data = {
            'name': 'test-client',
            'data_fetcher': {
                'source_type': 'invalid_source',
                'filter_rules': {}
            },
            'user_info': {
                'api-token': 'test-token',
                'user_id': 123
            }
        }
        
        config = ClientConfig.from_dict(config_data)
        
        # Try to create data fetcher with invalid source type
        try:
            data_fetcher = create_data_fetcher(config, logger)
            assert False, "Should have raised exception for invalid source type"
        except Exception as e:
            print(f"✅ Correctly raised exception for invalid source type: {e}")
        
        # Test with missing bitable configuration
        config_data['data_fetcher']['source_type'] = 'dingtalk_bitable'
        config = ClientConfig.from_dict(config_data)
        
        data_fetcher = create_data_fetcher(config, logger)
        
        context = WorkContext(
            client_name='test-client',
            batch_id='test-batch-error',
            client_config=config
        )
        
        try:
            result = data_fetcher.handle(context)
            assert False, "Should have raised exception for missing bitable config"
        except DataFetchException as e:
            print(f"✅ Correctly raised DataFetchException: {e.message}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error handling test failed: {e}")
        return False


def test_filtering_logic():
    """Test filtering logic in data fetcher."""
    print("\n" + "=" * 60)
    print("Testing Filtering Logic")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config()
        data_fetcher = create_data_fetcher(config, logger)
        
        # Test records with different update counts
        test_records = [
            {'work_url': 'https://www.xiaohongshu.com/discovery/item/686e6a68000000002203d213', 'update_count': 2},
            {'work_url': 'https://www.douyin.com/video/1234567890123456789', 'update_count': 6},  # Exceeds limit
            {'work_url': 'https://www.xiaohongshu.com/discovery/item/686e6a68000000002203d214', 'update_count': 1},
        ]
        
        filter_rules = {'update_times_limit': 5}
        
        # Test filtering
        filtered_records = data_fetcher.apply_filter_rules(test_records, filter_rules)
        
        # All records should pass since we don't have timestamp data for 7-day rule
        assert len(filtered_records) == 3, f"Expected 3 records, got {len(filtered_records)}"
        
        print(f"✅ Filtering test completed")
        print(f"   Original records: {len(test_records)}")
        print(f"   Filtered records: {len(filtered_records)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Filtering test failed: {e}")
        return False


def main():
    """Main test function."""
    print("=" * 60)
    print("Data Fetcher Integration Tests")
    print("=" * 60)
    
    tests = [
        test_data_fetcher_in_chain,
        test_error_handling,
        test_filtering_logic
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("INTEGRATION TEST RESULTS")
    print("=" * 60)
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total: {passed + failed}")
    
    if failed == 0:
        print("✅ All integration tests passed!")
        return True
    else:
        print("❌ Some integration tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
