#!/usr/bin/env python3
"""
Integration test for data sync in the complete chain of responsibility.
"""

import logging
import sys
from datetime import datetime
from unittest.mock import Mock, MagicMock, AsyncMock

# Add current directory to path for imports
sys.path.insert(0, '.')

from core.context import Work<PERSON>ontext, ClientConfig
from core.handlers.data_fetcher_factory import create_data_fetcher
from core.handlers.work_updater_factory import create_work_updater
from core.handlers.data_sync_factory import create_data_sync_handler
from core.chain_executor import ChainExecutor
from models.work_models import WorkDetailTaskModel
from core.exceptions import DataSyncException


class MockNotifier:
    """Mock notification handler for testing."""
    
    def __init__(self, client_config, logger):
        self.client_config = client_config
        self.logger = logger
        self.next_handler = None
        self.handler_name = self.__class__.__name__
    
    def set_next(self, handler):
        self.next_handler = handler
        return handler
    
    def handle(self, context):
        self.logger.info("MockNotifier: Processing notifications")
        
        # Simulate notification processing
        context.notifications_sent = len([w for w in context.synced_works if w.author_work and getattr(w.author_work, 'like_count', 0) > 50])
        
        self.logger.info(f"MockNotifier: Sent {context.notifications_sent} notifications")
        
        if self.next_handler:
            return self.next_handler.handle(context)
        return True


def setup_test_logging():
    """Setup logging for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger('test_integration')


def create_test_config():
    """Create a test client configuration."""
    config_data = {
        'name': 'test-client',
        'data_fetcher': {
            'source_type': 'dingtalk_bitable',
            'filter_rules': {
                'update_times_limit': 5
            }
        },
        'work_updater': {
            'platforms': ['xhs', 'dy'],
            'batch_size': 2
        },
        'data_sync': {
            'field_mapping': {
                '作品标题': 'title',
                '平台': 'platform_name',
                '作者': 'author_name',
                '作者主页': 'author_url',
                '正文': 'content',
                '作品发布时间': 'publish_time',
                '点赞数': 'like_count',
                '评论数': 'comment_count',
                '分享数': 'share_count',
                '收藏数': 'collect_count',
                '更新次数': 'update_count',
                '是否触发阈值': 'threshold',
                '更新时间': 'record_time'
            },
            'batch_size': 10
        },
        'user_info': {
            'api-token': 'test-token',
            'user_id': 123,
            'tenant': 'test-tenant'
        },
        'bitable': {
            'dentryUuid': 'test-dentry-uuid',
            'idOrName': 'test-table',
            'webhook': 'https://test-webhook.com/sync'
        }
    }
    
    return ClientConfig.from_dict(config_data)


def test_complete_chain_with_data_sync():
    """Test complete chain including data synchronization."""
    print("=" * 60)
    print("Testing Complete Chain with Data Sync")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        # Create test configuration
        config = create_test_config()
        
        # Create handlers
        data_fetcher = create_data_fetcher(config, logger)
        work_updater = create_work_updater(config, logger, 'basic')  # Use basic to avoid DB dependencies
        data_sync = create_data_sync_handler(config, logger, 'webhook')  # Use webhook for testing
        notifier = MockNotifier(config, logger)
        
        # Mock the bitable client for data fetcher
        mock_bitable_client = Mock()
        mock_records = [
            Mock(
                work_url='https://www.xiaohongshu.com/discovery/item/686e6a68000000002203d213',
                update_count=2,
                user_union_id='test-user',
                row_id='row123',
                extends={}
            ),
            Mock(
                work_url='https://www.douyin.com/video/1234567890123456789',
                update_count=1,
                user_union_id='test-user2',
                row_id='row456',
                extends={}
            )
        ]
        
        mock_bitable_client.list_bitable_data_by_api_filter.return_value = mock_records
        data_fetcher._bitable_client = mock_bitable_client
        
        # Mock platform handlers for work updater
        mock_xhs_handler = Mock()
        mock_dy_handler = AsyncMock()
        
        # Create mock work details
        mock_xhs_work = Mock()
        mock_xhs_work.title = "Test XHS Title"
        mock_xhs_work.content = "Test XHS content"
        mock_xhs_work.author_name = "XHS Author"
        mock_xhs_work.author_url = "https://www.xiaohongshu.com/user/profile/test"
        mock_xhs_work.publish_time = "2024-01-01 12:00:00"
        mock_xhs_work.like_count = 150
        mock_xhs_work.comment_count = 30
        mock_xhs_work.share_count = 5
        mock_xhs_work.collect_count = 15
        mock_xhs_work.work_id = "686e6a68000000002203d213"
        
        mock_dy_work = Mock()
        mock_dy_work.title = "Test DY Title"
        mock_dy_work.content = "Test DY content"
        mock_dy_work.author_name = "DY Author"
        mock_dy_work.author_url = "https://www.douyin.com/user/test"
        mock_dy_work.publish_time = "2024-01-02 15:30:00"
        mock_dy_work.like_count = 250
        mock_dy_work.comment_count = 60
        mock_dy_work.share_count = 10
        mock_dy_work.collect_count = 30
        mock_dy_work.work_id = "1234567890123456789"
        
        # Configure mock handlers
        mock_xhs_handler.query_article_detail.return_value = mock_xhs_work
        mock_dy_handler.query_article_detail.return_value = mock_dy_work
        
        # Replace platform handlers
        work_updater._platform_handlers = {
            'xhs': mock_xhs_handler,
            'dy': mock_dy_handler
        }
        
        # Mock data sync client
        mock_sync_client = Mock()
        mock_sync_client.constom_invoke.return_value = True
        data_sync._data_sync_client = mock_sync_client
        
        # Chain handlers together
        data_fetcher.set_next(work_updater).set_next(data_sync).set_next(notifier)
        
        # Create chain executor
        executor = ChainExecutor(config, logger)
        executor.set_chain(data_fetcher)
        
        # Execute the complete chain
        context = executor.execute("test-batch-integration")
        
        # Verify results at each stage
        assert len(context.raw_tasks) == 2, f"Expected 2 raw tasks, got {len(context.raw_tasks)}"
        assert len(context.formatted_tasks) == 2, f"Expected 2 formatted tasks, got {len(context.formatted_tasks)}"
        assert len(context.updated_works) == 2, f"Expected 2 updated works, got {len(context.updated_works)}"
        assert len(context.synced_works) == 2, f"Expected 2 synced works, got {len(context.synced_works)}"
        assert context.notifications_sent == 2, f"Expected 2 notifications, got {context.notifications_sent}"
        
        # Verify that data sync was called
        assert mock_sync_client.constom_invoke.called, "Data sync should have been called"
        
        # Check sync data
        call_args = mock_sync_client.constom_invoke.call_args
        webhook_data = call_args.kwargs['json_data']
        
        assert 'dentryUuid' in webhook_data, "Missing dentryUuid in webhook data"
        assert 'idOrName' in webhook_data, "Missing idOrName in webhook data"
        assert 'updateRecords' in webhook_data, "Missing updateRecords in webhook data"
        assert len(webhook_data['updateRecords']) == 2, f"Expected 2 update records, got {len(webhook_data['updateRecords'])}"
        
        # Check field mapping in sync data
        first_record = webhook_data['updateRecords'][0]
        assert 'fields' in first_record, "Missing fields in sync record"
        
        fields = first_record['fields']
        assert '作品标题' in fields, "Missing mapped field: 作品标题"
        assert '平台' in fields, "Missing mapped field: 平台"
        assert '点赞数' in fields, "Missing mapped field: 点赞数"
        
        print("✅ Complete chain with data sync test passed")
        print(f"   Raw tasks: {len(context.raw_tasks)}")
        print(f"   Formatted tasks: {len(context.formatted_tasks)}")
        print(f"   Updated works: {len(context.updated_works)}")
        print(f"   Synced works: {len(context.synced_works)}")
        print(f"   Notifications sent: {context.notifications_sent}")
        print(f"   Sync records: {len(webhook_data['updateRecords'])}")
        
        # Check execution summary
        summary = context.to_summary()
        print(f"\nExecution Summary:")
        print(f"   Execution time: {summary['execution_time']:.3f}s")
        print(f"   Success rate: {summary['success_rate']:.1f}%")
        print(f"   Total processed: {summary['total_processed']}")
        print(f"   Successful updates: {summary['successful_updates']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Complete chain with data sync test failed: {e}")
        return False


def test_data_sync_field_mapping_integration():
    """Test field mapping in data sync integration."""
    print("\n" + "=" * 60)
    print("Testing Data Sync Field Mapping Integration")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config()
        data_sync = create_data_sync_handler(config, logger, 'webhook')
        
        # Mock sync client
        mock_sync_client = Mock()
        mock_sync_client.constom_invoke.return_value = True
        data_sync._data_sync_client = mock_sync_client
        
        # Create test context with updated works
        context = WorkContext(
            client_name='test-client',
            batch_id='test-batch-mapping',
            client_config=config
        )
        
        # Create mock work with detailed data
        mock_work = Mock()
        mock_work.title = "Integration Test Title"
        mock_work.content = "Integration test content with detailed information"
        mock_work.author_name = "Integration Test Author"
        mock_work.author_url = "https://test-platform.com/user/integration"
        mock_work.publish_time = "2024-01-15 10:30:00"
        mock_work.like_count = 500
        mock_work.comment_count = 100
        mock_work.share_count = 25
        mock_work.collect_count = 75
        
        context.updated_works = [
            WorkDetailTaskModel(
                record_id='test-batch-mapping',
                work_url='https://www.xiaohongshu.com/discovery/item/integration123',
                work_id='integration123',
                platform='xhs',
                row_id='integration-row',
                dentry_uuid='test-dentry-uuid',
                id_or_name='test-table',
                update_count=3,
                threshold='是',
                author_work=mock_work
            )
        ]
        
        # Process sync
        result = data_sync.handle(context)
        
        assert result == True, "Data sync should succeed"
        assert len(context.synced_works) == 1, f"Expected 1 synced work, got {len(context.synced_works)}"
        
        # Verify sync was called
        assert mock_sync_client.constom_invoke.called, "Sync should have been called"
        
        # Check detailed field mapping
        call_args = mock_sync_client.constom_invoke.call_args
        webhook_data = call_args.kwargs['json_data']
        
        record = webhook_data['updateRecords'][0]
        fields = record['fields']
        
        # Verify all mapped fields
        expected_mappings = {
            '作品标题': 'Integration Test Title',
            '平台': '小红书',
            '作者': 'Integration Test Author',
            '作者主页': 'https://test-platform.com/user/integration',
            '正文': 'Integration test content with detailed information',
            '作品发布时间': '2024-01-15 10:30:00',
            '点赞数': 500,
            '评论数': 100,
            '分享数': 25,
            '收藏数': 75,
            '更新次数': 4,  # Should be incremented
            '是否触发阈值': '是'
        }
        
        for field_name, expected_value in expected_mappings.items():
            assert field_name in fields, f"Missing field: {field_name}"
            assert fields[field_name] == expected_value, f"Field {field_name}: expected {expected_value}, got {fields[field_name]}"
        
        print("✅ Data sync field mapping integration test passed")
        print(f"   Mapped fields: {len(fields)}")
        print(f"   Title: {fields['作品标题']}")
        print(f"   Platform: {fields['平台']}")
        print(f"   Like count: {fields['点赞数']}")
        print(f"   Update count: {fields['更新次数']}")
        print(f"   Threshold: {fields['是否触发阈值']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Field mapping integration test failed: {e}")
        return False


def main():
    """Main test function."""
    print("=" * 60)
    print("Data Sync Integration Tests")
    print("=" * 60)
    
    tests = [
        test_complete_chain_with_data_sync,
        test_data_sync_field_mapping_integration
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("INTEGRATION TEST RESULTS")
    print("=" * 60)
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total: {passed + failed}")
    
    if failed == 0:
        print("✅ All integration tests passed!")
        return True
    else:
        print("❌ Some integration tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
