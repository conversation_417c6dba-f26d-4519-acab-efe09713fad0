#!/usr/bin/env python3
"""
Test script for data synchronization handlers.
"""

import logging
import sys
from datetime import datetime
from unittest.mock import Mock, MagicMock

# Add current directory to path for imports
sys.path.insert(0, '.')

from core.context import WorkContext, ClientConfig
from core.handlers.data_sync_factory import create_data_sync_handler
from core.handlers.dingtalk_data_sync import DingTalkDataSync
from core.handlers.webhook_data_sync import WebhookDataSync
from models.work_models import WorkDetailTaskModel
from core.exceptions import DataSyncException


def setup_test_logging():
    """Setup logging for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger('test_data_sync')


def create_test_config(sync_type='auto'):
    """Create a test client configuration."""
    config_data = {
        'name': 'test-client',
        'data_sync': {
            'field_mapping': {
                '作品标题': 'title',
                '平台': 'platform_name',
                '作者': 'author_name',
                '作者主页': 'author_url',
                '正文': 'content',
                '作品发布时间': 'publish_time',
                '点赞数': 'like_count',
                '评论数': 'comment_count',
                '分享数': 'share_count',
                '收藏数': 'collect_count',
                '更新次数': 'update_count',
                '是否触发阈值': 'threshold',
                '更新时间': 'record_time'
            },
            'batch_size': 10
        },
        'user_info': {
            'api-token': 'test-token',
            'user_id': 123,
            'tenant': 'test-tenant'
        },
        'bitable': {
            'dentryUuid': 'test-dentry-uuid',
            'idOrName': 'test-table'
        }
    }
    
    # Add webhook for webhook sync type
    if sync_type == 'webhook':
        config_data['bitable']['webhook'] = 'https://test-webhook.com/sync'
    
    return ClientConfig.from_dict(config_data)


def create_test_works():
    """Create test works for synchronization."""
    # Create mock author works
    mock_xhs_work = Mock()
    mock_xhs_work.title = "Test XHS Title"
    mock_xhs_work.content = "Test XHS content"
    mock_xhs_work.author_name = "XHS Author"
    mock_xhs_work.author_url = "https://www.xiaohongshu.com/user/profile/test"
    mock_xhs_work.publish_time = "2024-01-01 12:00:00"
    mock_xhs_work.like_count = 100
    mock_xhs_work.comment_count = 20
    mock_xhs_work.share_count = 5
    mock_xhs_work.collect_count = 15
    
    mock_dy_work = Mock()
    mock_dy_work.title = "Test DY Title"
    mock_dy_work.content = "Test DY content"
    mock_dy_work.author_name = "DY Author"
    mock_dy_work.author_url = "https://www.douyin.com/user/test"
    mock_dy_work.publish_time = "2024-01-02 15:30:00"
    mock_dy_work.like_count = 200
    mock_dy_work.comment_count = 50
    mock_dy_work.share_count = 10
    mock_dy_work.collect_count = 30
    
    return [
        WorkDetailTaskModel(
            record_id='test-batch-001',
            work_url='https://www.xiaohongshu.com/discovery/item/686e6a68000000002203d213',
            work_id='686e6a68000000002203d213',
            platform='xhs',
            row_id='row1',
            dentry_uuid='test-dentry-uuid',
            id_or_name='test-table',
            update_count=2,
            threshold='否',
            author_work=mock_xhs_work
        ),
        WorkDetailTaskModel(
            record_id='test-batch-001',
            work_url='https://www.douyin.com/video/1234567890123456789',
            work_id='1234567890123456789',
            platform='dy',
            row_id='row2',
            dentry_uuid='test-dentry-uuid',
            id_or_name='test-table',
            update_count=1,
            threshold='否',
            author_work=mock_dy_work
        )
    ]


def test_data_sync_factory():
    """Test the data sync factory."""
    print("=" * 60)
    print("Testing Data Sync Factory")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        # Test auto-detection (should detect webhook)
        config = create_test_config('webhook')
        sync_handler = create_data_sync_handler(config, logger)
        
        assert isinstance(sync_handler, WebhookDataSync), f"Expected WebhookDataSync, got {type(sync_handler)}"
        print("✅ Webhook data sync handler created successfully (auto-detected)")
        
        # Test auto-detection (should detect dingtalk_bitable)
        config = create_test_config('dingtalk_bitable')
        sync_handler = create_data_sync_handler(config, logger)
        
        assert isinstance(sync_handler, DingTalkDataSync), f"Expected DingTalkDataSync, got {type(sync_handler)}"
        print("✅ DingTalk data sync handler created successfully (auto-detected)")
        
        # Test explicit type
        sync_handler = create_data_sync_handler(config, logger, 'webhook')
        assert isinstance(sync_handler, WebhookDataSync), f"Expected WebhookDataSync, got {type(sync_handler)}"
        print("✅ Webhook data sync handler created successfully (explicit)")
        
        # Test unsupported sync type
        try:
            sync_handler = create_data_sync_handler(config, logger, 'unsupported_type')
            assert False, "Should have raised exception for unsupported type"
        except Exception as e:
            print(f"✅ Correctly raised exception for unsupported type: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"Factory test failed: {e}")
        return False


def test_field_mapping():
    """Test field mapping functionality."""
    print("\n" + "=" * 60)
    print("Testing Field Mapping")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config('dingtalk_bitable')
        sync_handler = create_data_sync_handler(config, logger, 'dingtalk_bitable')
        
        # Create test work
        test_works = create_test_works()
        test_work = test_works[0]  # XHS work
        
        # Test field mapping
        field_mapping = config.field_mapping
        mapped_fields = sync_handler._apply_field_mapping(test_work, field_mapping)
        
        # Verify mapped fields
        assert '作品标题' in mapped_fields, "Missing mapped field: 作品标题"
        assert '平台' in mapped_fields, "Missing mapped field: 平台"
        assert '作者' in mapped_fields, "Missing mapped field: 作者"
        assert '点赞数' in mapped_fields, "Missing mapped field: 点赞数"
        
        assert mapped_fields['作品标题'] == "Test XHS Title"
        assert mapped_fields['平台'] == "小红书"
        assert mapped_fields['作者'] == "XHS Author"
        assert mapped_fields['点赞数'] == 100
        assert mapped_fields['更新次数'] == 3  # Should be incremented
        
        print("✅ Field mapping test passed")
        print(f"   Mapped fields: {len(mapped_fields)}")
        print(f"   Title: {mapped_fields['作品标题']}")
        print(f"   Platform: {mapped_fields['平台']}")
        print(f"   Author: {mapped_fields['作者']}")
        print(f"   Like count: {mapped_fields['点赞数']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Field mapping test failed: {e}")
        return False


def test_webhook_data_sync():
    """Test webhook data sync with mocked dependencies."""
    print("\n" + "=" * 60)
    print("Testing Webhook Data Sync")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config('webhook')
        sync_handler = create_data_sync_handler(config, logger, 'webhook')
        
        # Mock the data sync client
        mock_client = Mock()
        mock_client.constom_invoke.return_value = True
        sync_handler._data_sync_client = mock_client
        
        # Create test context
        context = WorkContext(
            client_name='test-client',
            batch_id='test-batch-001',
            client_config=config
        )
        
        # Set updated works
        context.updated_works = create_test_works()
        
        # Process sync
        result = sync_handler.handle(context)
        
        assert result == True, "Handler should return True"
        assert len(context.synced_works) == 2, f"Expected 2 synced works, got {len(context.synced_works)}"
        
        # Verify that webhook was called
        assert mock_client.constom_invoke.called, "Webhook should have been called"
        
        # Check webhook data
        call_args = mock_client.constom_invoke.call_args
        webhook_data = call_args.kwargs['json_data']
        
        assert 'dentryUuid' in webhook_data, "Missing dentryUuid in webhook data"
        assert 'idOrName' in webhook_data, "Missing idOrName in webhook data"
        assert 'updateRecords' in webhook_data, "Missing updateRecords in webhook data"
        assert len(webhook_data['updateRecords']) == 2, f"Expected 2 update records, got {len(webhook_data['updateRecords'])}"
        
        print(f"✅ Webhook data sync test passed")
        print(f"   Synced works: {len(context.synced_works)}")
        print(f"   Webhook called: {mock_client.constom_invoke.called}")
        print(f"   Update records: {len(webhook_data['updateRecords'])}")
        
        return True
        
    except Exception as e:
        logger.error(f"Webhook data sync test failed: {e}")
        return False


def test_work_grouping():
    """Test work grouping by target."""
    print("\n" + "=" * 60)
    print("Testing Work Grouping")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config('dingtalk_bitable')
        sync_handler = create_data_sync_handler(config, logger, 'dingtalk_bitable')
        
        # Create works with different targets
        works = create_test_works()
        
        # Add a work with different target
        mock_work3 = Mock()
        mock_work3.title = "Test Work 3"
        mock_work3.author_name = "Author 3"
        mock_work3.like_count = 50
        
        works.append(WorkDetailTaskModel(
            record_id='test-batch-001',
            work_url='https://www.xiaohongshu.com/discovery/item/test3',
            work_id='test3',
            platform='xhs',
            row_id='row3',
            dentry_uuid='different-dentry-uuid',
            id_or_name='different-table',
            update_count=1,
            threshold='否',
            author_work=mock_work3
        ))
        
        # Test grouping
        grouped_works = sync_handler._group_works_by_target(works)
        
        assert len(grouped_works) == 2, f"Expected 2 groups, got {len(grouped_works)}"
        
        # Check group keys
        expected_key1 = "test-dentry-uuid#test-table"
        expected_key2 = "different-dentry-uuid#different-table"
        
        assert expected_key1 in grouped_works, f"Missing group key: {expected_key1}"
        assert expected_key2 in grouped_works, f"Missing group key: {expected_key2}"
        
        assert len(grouped_works[expected_key1]) == 2, f"Expected 2 works in group 1, got {len(grouped_works[expected_key1])}"
        assert len(grouped_works[expected_key2]) == 1, f"Expected 1 work in group 2, got {len(grouped_works[expected_key2])}"
        
        print("✅ Work grouping test passed")
        print(f"   Total groups: {len(grouped_works)}")
        print(f"   Group 1 ({expected_key1}): {len(grouped_works[expected_key1])} works")
        print(f"   Group 2 ({expected_key2}): {len(grouped_works[expected_key2])} works")
        
        return True
        
    except Exception as e:
        logger.error(f"Work grouping test failed: {e}")
        return False


def test_error_handling():
    """Test error handling in data sync."""
    print("\n" + "=" * 60)
    print("Testing Error Handling")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config('webhook')
        sync_handler = create_data_sync_handler(config, logger, 'webhook')
        
        # Mock client that fails
        mock_client = Mock()
        mock_client.constom_invoke.side_effect = Exception("Mock sync error")
        sync_handler._data_sync_client = mock_client
        
        # Create test context
        context = WorkContext(
            client_name='test-client',
            batch_id='test-batch-error',
            client_config=config
        )
        
        # Set updated works
        context.updated_works = create_test_works()
        
        # Process sync (should handle errors gracefully)
        try:
            result = sync_handler.handle(context)
            # Should continue chain even with sync errors
            assert result == True, "Handler should return True even with sync errors"
            assert len(context.synced_works) == 0, f"Expected 0 synced works, got {len(context.synced_works)}"
            # Note: error_messages might be empty if errors are handled internally

            print("✅ Error handling test passed")
            print(f"   Synced works: {len(context.synced_works)}")
            print(f"   Error messages: {len(context.error_messages)}")

        except DataSyncException as e:
            # This is also acceptable - sync handler can raise exception
            print("✅ Error handling test passed (exception raised)")
            print(f"   Exception: {e.message}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error handling test failed: {e}")
        return False


def main():
    """Main test function."""
    print("=" * 60)
    print("Testing Data Synchronization Handlers")
    print("=" * 60)
    
    tests = [
        test_data_sync_factory,
        test_field_mapping,
        test_webhook_data_sync,
        test_work_grouping,
        test_error_handling
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("TEST RESULTS")
    print("=" * 60)
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total: {passed + failed}")
    
    if failed == 0:
        print("✅ All tests passed!")
        return True
    else:
        print("❌ Some tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
