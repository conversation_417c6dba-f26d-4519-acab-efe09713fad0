"""
数据获取Handler - 负责从数据源获取待处理的作品数据

支持两种数据获取方式：
1. 从服务器查询 (需要api_token) - 参考 old/xhs_detail_updater.py 的 __query_tasks_rows 方法
2. 从多维表获取 (使用jss-api-extend) - 参考 jss_api_extend/bitable/base_dingtalk_bitable_notifier.py 的 query_records 方法
"""

import logging
import re
from abc import abstractmethod
from typing import List, Dict, Any, Optional

from jss_api_extend import BaseDingTalkBitableNotifier
from models.work_models import WorkDetailTaskModel
from .base_handler import BaseHandler
from ..context import ClientConfig
from ..exceptions import DataFetchException
from ..pipeline_context import PipelineContext


class DataHandler(BaseHandler):
    """
    数据获取处理器基类

    职责：
    - 从配置的数据源获取待处理的作品列表
    - 统一处理为标准的数据格式
    - 应用过滤规则
    - 设置上下文中的待处理数据
    """

    def __init__(self, client_config: ClientConfig, logger: logging.Logger):
        super().__init__(client_config, logger)
        self.logger = logger.getChild(self.__class__.__name__)

    def handle(self, context: PipelineContext) -> PipelineContext:
        """
        处理数据获取逻辑

        """
        try:
            self.logger.info(f"Starting data fetch for client: {context.client_name}")

            # 获取数据
            tasks = self._fetch_data(context)

            # 应用过滤规则
            filtered_tasks = self._apply_filters(tasks, context)

            # 设置到上下文
            context.pending_tasks = filtered_tasks
            context.total_tasks = len(filtered_tasks)

            self.logger.info(f"Fetched {len(filtered_tasks)} tasks for processing")

            # 传递给下一个处理器
            return super().handle(context)

        except Exception as e:
            self.logger.error(f"Data fetch failed: {e}")
            raise DataFetchException(f"Failed to fetch data: {str(e)}")

    @abstractmethod
    def _fetch_data(self, context: WorkContext) -> List[WorkDetailTaskModel]:
        """
        从数据源获取数据 - 子类必须实现
        
        Args:
            context: 工作上下文
            
        Returns:
            待处理的作品任务列表
        """
        pass

    def _apply_filters(self, tasks: List[WorkDetailTaskModel]) -> List[WorkDetailTaskModel]:
        """
        应用过滤规则
        
        Args:
            tasks: 原始任务列表
            context: 工作上下文
            
        Returns:
            过滤后的任务列表
        """
        if not tasks:
            return tasks

        filtered_tasks = tasks

        # 应用更新次数限制
        update_limit = self._get_update_limit()
        if update_limit > 0:
            filtered_tasks = [
                task for task in filtered_tasks
                if task.update_count < update_limit
            ]
            self.logger.info(f"Applied update limit filter: {len(filtered_tasks)} tasks remaining")

        # 应用平台过滤
        allowed_platforms = self._get_allowed_platforms()
        if allowed_platforms:
            filtered_tasks = [
                task for task in filtered_tasks
                if task.platform in allowed_platforms
            ]
            self.logger.info(f"Applied platform filter: {len(filtered_tasks)} tasks remaining")

        return filtered_tasks

    def _get_update_limit(self) -> int:
        """获取更新次数限制"""
        data_fetcher_config = self.client_config.data_fetcher_config or {}
        filter_rules = data_fetcher_config.get('filter_rules', {})
        return filter_rules.get('update_times_limit', 0)

    def _get_allowed_platforms(self) -> List[str]:
        """获取允许的平台列表"""
        work_updater_config = self.client_config.work_updater_config or {}
        return work_updater_config.get('platforms', [])

    def _extract_platform_and_work_id(self, work_url: str) -> tuple[str, str]:
        """
        从作品URL提取平台和作品ID

        Args:
            work_url: 作品URL

        Returns:
            (platform, work_id) 元组
        """
        if not work_url:
            return None, None

        # 小红书
        if 'xiaohongshu.com' in work_url or 'xhslink.com' in work_url:
            match = re.search(r'/item/([a-f0-9]+)', work_url)
            if match:
                return 'xhs', match.group(1)

        # 抖音
        elif 'douyin.com' in work_url:
            match = re.search(r'/video/(\d+)', work_url)
            if match:
                return 'dy', match.group(1)

        # 快手
        elif 'kuaishou.com' in work_url:
            match = re.search(r'/photo/(\d+)', work_url)
            if match:
                return 'ks', match.group(1)

        # 微信视频号
        elif 'channels.weixin.qq.com' in work_url:
            match = re.search(r'/video/([a-zA-Z0-9_-]+)', work_url)
            if match:
                return 'wxvideo', match.group(1)

        # 默认返回URL的hash作为work_id
        return 'unknown', str(hash(work_url))


class BitableDataHandler(DataHandler):
    """
    从钉钉多维表格获取数据的处理器

    参考 jss_api_extend/bitable/base_dingtalk_bitable_notifier.py 的 query_records 方法实现
    每个客户的条件是不同的，所以需要继承实现
    """

    def __init__(self, client_config: ClientConfig, logger: logging.Logger):
        super().__init__(client_config, logger)
        self._bitable_client = None
        self.dingtalk_notifier_client = BaseDingTalkBitableNotifier(
            client_config.bitable.get("app_key"),
            client_config.bitable.get("app_secret"),
            client_config.bitable.get("agent_id"),
            client_config.bitable.get("operator_id")
        )

    def _fetch_raw_data(self, context: PipelineContext) -> List[Dict[str, Any]]:
        """
        从钉钉多维表格获取数据

        Args:
            context: Pipeline上下文

        Returns:
            原始数据列表
        """
        try:
            bitable_client = self._get_bitable_client()

            # 获取表格配置
            dentry_uuid = self.client_config.bitable.get('dentryUuid')
            id_or_name = self.client_config.bitable.get('idOrName')

            if not dentry_uuid or not id_or_name:
                return None

            # 获取过滤条件 - 子类可以重写此方法
            filter_conditions = self._get_filter_conditions(context)
            max_results = self._get_max_results()

            self.logger.info(f"Querying bitable: {dentry_uuid}/{id_or_name}, max_results: {max_results}")

            # 查询记录
            records = bitable_client.query_records(
                dentry_uuid=dentry_uuid,
                id_or_name=id_or_name,
                filter_conditions=filter_conditions,
                max_results=max_results
            )

            if not records:
                self.logger.info("No records found from bitable")
                return []

            self.logger.info(f"Successfully fetched {len(records)} records from bitable")
            context.data_source_type = "bitable"

            return records

        except Exception as e:
            self.logger.error(f"Failed to fetch data from bitable: {e}")
            raise DataFetchException(f"Bitable data fetch failed: {str(e)}")

    def _convert_single_item(self, item: Dict[str, Any]) -> Optional[WorkDetailTaskModel]:
        """
        转换多维表格返回的单个数据项

        Args:
            item: 多维表格返回的数据项
            context: Pipeline上下文

        Returns:
            转换后的任务模型
        """
        try:
            # 从fields中提取数据
            fields = item.get("fields", {})
            record_id = item.get("recordId", "")

            # 提取作品URL - 字段名可能因客户而异，子类可以重写
            work_url = self._extract_work_url(fields)
            if not work_url:
                return None

            # 提取平台和作品ID
            platform, work_id = self._extract_platform_and_work_id(work_url)
            if not platform or not work_id:
                self.logger.warning(f"Failed to extract platform/work_id from URL: {work_url}")
                return None

            # 提取其他字段 - 子类可以重写这些方法
            update_count = self._extract_update_count(fields)
            threshold = self._extract_threshold(fields)
            submit_user = self._extract_submit_user(fields)

            # 创建任务模型
            task = WorkDetailTaskModel(
                id=record_id,
                work_url=work_url,
                work_id=work_id,
                platform=platform,
                row_id=record_id,
                update_count=update_count,
                threshold=threshold,
                submit_user=submit_user,
            )

            return task

        except Exception as e:
            self.logger.error(f"Failed to convert bitable item: {e}")
            return None

    def _get_filter_conditions(self, context: PipelineContext) -> Optional[List[Dict[str, Any]]]:
        """
        获取过滤条件 - 子类可以重写此方法实现自定义过滤

        Args:
            context: Pipeline上下文

        Returns:
            过滤条件列表
        """
        # 默认过滤条件：获取待处理的记录
        return [
            {"field": "状态", "operator": "equal", "value": ["待处理"]}
        ]

    def _get_max_results(self) -> int:
        """获取最大结果数量"""
        data_fetcher_config = self.client_config.data_fetcher_config or {}
        return data_fetcher_config.get('limit', 100)

    def _extract_work_url(self, fields: Dict[str, Any]) -> Optional[str]:
        """
        从字段中提取作品URL - 子类可以重写此方法

        Args:
            fields: 记录字段

        Returns:
            作品URL
        """
        # 常见的字段名
        possible_fields = ['作品链接', '链接', 'workUrl', 'url', '作品地址']

        for field_name in possible_fields:
            if field_name in fields:
                value = fields[field_name]
                if isinstance(value, list) and value:
                    return value[0].get('text', '') if isinstance(value[0], dict) else str(value[0])
                elif isinstance(value, str):
                    return value

        return None

    def _extract_update_count(self, fields: Dict[str, Any]) -> int:
        """
        从字段中提取更新次数 - 子类可以重写此方法

        Args:
            fields: 记录字段

        Returns:
            更新次数
        """
        possible_fields = ['更新次数', 'updateCount', '次数']

        for field_name in possible_fields:
            if field_name in fields:
                value = fields[field_name]
                try:
                    return int(value) if value else 0
                except (ValueError, TypeError):
                    continue

        return 0

    def _extract_threshold(self, fields: Dict[str, Any]) -> str:
        """
        从字段中提取阈值状态 - 子类可以重写此方法

        Args:
            fields: 记录字段

        Returns:
            阈值状态 ("是" 或 "否")
        """
        possible_fields = ['阈值', 'threshold', '是否达到阈值']

        for field_name in possible_fields:
            if field_name in fields:
                value = fields[field_name]
                if isinstance(value, list) and value:
                    return value[0] if isinstance(value[0], str) else "否"
                elif isinstance(value, str):
                    return value

        return "否"

    def _extract_submit_user(self, fields: Dict[str, Any]) -> List[str]:
        """
        从字段中提取提交用户 - 子类可以重写此方法

        Args:
            fields: 记录字段

        Returns:
            提交用户列表
        """
        possible_fields = ['提交人', 'submitUser', '用户', '创建人']

        for field_name in possible_fields:
            if field_name in fields:
                value = fields[field_name]
                if isinstance(value, list):
                    return [str(item) for item in value]
                elif isinstance(value, str):
                    return [value]

        return []

    def _get_bitable_client(self):
        """获取钉钉多维表格客户端"""
        if self._bitable_client is None:
            try:
                from jss_api_extend.dingtalk_bitable_factory import DingtalkBitableFactory

                bitable_config = self.client_config.
                if not bitable_config:
                    raise DataFetchException("Missing bitable configuration")

                self._bitable_client = DingtalkBitableFactory.create_client(
                    bitable_config, self.logger
                )

            except ImportError as e:
                self.logger.error(f"Failed to import bitable factory: {e}")
                raise DataFetchException("Bitable client not available")
            except Exception as e:
                self.logger.error(f"Failed to create bitable client: {e}")
                raise DataFetchException(f"Bitable client creation failed: {str(e)}")

        return self._bitable_client

