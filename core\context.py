"""
Context classes for the work monitor system.

This module defines the context objects that carry data through
the chain of responsibility handlers.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging


@dataclass
class ClientConfig:
    """Configuration for a specific client."""

    name: str

    # 调度配置
    schedule: Dict[str, Any] = field(default_factory=dict)

    # 数据获取配置
    data_fetcher: Dict[str, Any] = field(default_factory=dict)

    # 作品更新配置
    work_updater: Dict[str, Any] = field(default_factory=dict)

    # 通知配置
    notification: Dict[str, Any] = field(default_factory=dict)

    # 用户信息配置
    user_info: Dict[str, Any] = field(default_factory=dict)

    # bitable配置
    bitable: Dict[str, Any] = field(default_factory=dict)

    # 数据同步配置
    data_sync: Dict[str, Any] = field(default_factory=dict)
    


    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ClientConfig':
        """Create ClientConfig from dictionary."""
        return cls(
            name=data.get('name', ''),
            schedule=data.get('schedule', {}),
            data_fetcher=data.get('data_fetcher', {}),
            work_updater=data.get('work_updater', {}),
            notification=data.get('notification', {}),
            user_info=data.get('user_info', {}),
            bitable=data.get('bitable', {}),
            data_sync=data.get('data_sync', {})
        )


@dataclass
class WorkContext:
    """Context object that carries data through the handler chain."""
    
    # Basic information
    client_name: str
    batch_id: str
    start_time: datetime = field(default_factory=datetime.now)
    
    # Configuration
    client_config: Optional[ClientConfig] = None
    
    # Data at different stages
    raw_tasks: List[Any] = field(default_factory=list)  # Raw tasks from data source
    formatted_tasks: List[Any] = field(default_factory=list)  # Formatted task models
    updated_works: List[Any] = field(default_factory=list)  # Works with updated details
    synced_works: List[Any] = field(default_factory=list)  # Successfully synced works
    
    # Error tracking
    failed_tasks: List[str] = field(default_factory=list)
    failed_urls: List[str] = field(default_factory=list)
    error_messages: List[str] = field(default_factory=list)
    
    # Metrics
    total_processed: int = 0
    successful_updates: int = 0
    failed_updates: int = 0
    notifications_sent: int = 0
    
    # Additional metadata
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_error(self, error_msg: str, failed_item: Optional[str] = None):
        """Add an error to the context."""
        self.error_messages.append(error_msg)
        if failed_item:
            self.failed_tasks.append(failed_item)
    
    def add_failed_url(self, url: str):
        """Add a failed URL to the context."""
        self.failed_urls.append(url)
    
    def get_execution_time(self) -> float:
        """Get execution time in seconds."""
        return (datetime.now() - self.start_time).total_seconds()
    
    def get_success_rate(self) -> float:
        """Get success rate as percentage."""
        if self.total_processed == 0:
            return 0.0
        return (self.successful_updates / self.total_processed) * 100
    
    def to_summary(self) -> Dict[str, Any]:
        """Convert context to summary dictionary."""
        return {
            'client_name': self.client_name,
            'batch_id': self.batch_id,
            'execution_time': self.get_execution_time(),
            'total_processed': self.total_processed,
            'successful_updates': self.successful_updates,
            'failed_updates': self.failed_updates,
            'success_rate': self.get_success_rate(),
            'notifications_sent': self.notifications_sent,
            'failed_urls_count': len(self.failed_urls),
            'error_count': len(self.error_messages)
        }
