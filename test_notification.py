#!/usr/bin/env python3
"""
Test script for notification handlers.
"""

import logging
import sys
from datetime import datetime
from unittest.mock import Mock, MagicMock

# Add current directory to path for imports
sys.path.insert(0, '.')

from core.context import WorkContext, ClientConfig
from core.handlers.notification_factory import create_notification_handler, NotificationFactory
from core.handlers.dingtalk_notification import DingTalkNotification
from core.handlers.email_notification import EmailNotification
from models.work_models import WorkDetailTaskModel
from core.exceptions import NotificationException


def setup_test_logging():
    """Setup logging for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger('test_notification')


def create_test_config(notification_type='dingtalk'):
    """Create a test client configuration."""
    config_data = {
        'name': 'test-client',
        'notification': {
            'enabled_channels': ['dingtalk'] if notification_type == 'dingtalk' else ['email'],
            'threshold_rules': {
                'test-client': {
                    'like_count': 50,
                    'comment_count': 20
                },
                'default': {
                    'like_count': 100,
                    'comment_count': 30
                }
            },
            'message_templates': {
                'test-client': '作品阈值通知：标题={title}, 作者={author_name}, 平台={platform}, {threshold_info}, 作品链接={work_url}',
                'default': '作品阈值通知：{title} - {author_name} ({platform})'
            },
            'default_recipients': [
                {'unionId': 'default-user-1'},
                {'unionId': 'default-user-2'}
            ]
        },
        'user_info': {
            'api-token': 'test-token',
            'user_id': 123,
            'tenant': 'test-tenant'
        }
    }
    
    # Add email configuration for email notifications
    if notification_type == 'email':
        config_data['notification']['email_config'] = {
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'username': '<EMAIL>',
            'password': 'test-password',
            'from_address': '<EMAIL>'
        }
    
    return ClientConfig.from_dict(config_data)


def create_test_works():
    """Create test works for notification testing."""
    # Create mock author works
    mock_high_engagement_work = Mock()
    mock_high_engagement_work.title = "High Engagement Work"
    mock_high_engagement_work.content = "This work has high engagement"
    mock_high_engagement_work.author_name = "Popular Author"
    mock_high_engagement_work.author_url = "https://www.xiaohongshu.com/user/profile/popular"
    mock_high_engagement_work.publish_time = "2024-01-01 12:00:00"
    mock_high_engagement_work.like_count = 150  # Above threshold
    mock_high_engagement_work.comment_count = 50  # Above threshold
    mock_high_engagement_work.share_count = 10
    mock_high_engagement_work.collect_count = 25
    
    mock_low_engagement_work = Mock()
    mock_low_engagement_work.title = "Low Engagement Work"
    mock_low_engagement_work.content = "This work has low engagement"
    mock_low_engagement_work.author_name = "Regular Author"
    mock_low_engagement_work.author_url = "https://www.xiaohongshu.com/user/profile/regular"
    mock_low_engagement_work.publish_time = "2024-01-02 15:30:00"
    mock_low_engagement_work.like_count = 30  # Below threshold
    mock_low_engagement_work.comment_count = 10  # Below threshold
    mock_low_engagement_work.share_count = 2
    mock_low_engagement_work.collect_count = 5
    
    return [
        WorkDetailTaskModel(
            record_id='test-batch-001',
            work_url='https://www.xiaohongshu.com/discovery/item/high-engagement',
            work_id='high-engagement',
            platform='xhs',
            row_id='row1',
            dentry_uuid='test-dentry-uuid',
            id_or_name='test-table',
            update_count=2,
            threshold='否',
            author_work=mock_high_engagement_work,
            submit_user=[
                {'unionId': 'user-1', 'name': 'Test User 1'},
                {'unionId': 'user-2', 'name': 'Test User 2'}
            ]
        ),
        WorkDetailTaskModel(
            record_id='test-batch-001',
            work_url='https://www.xiaohongshu.com/discovery/item/low-engagement',
            work_id='low-engagement',
            platform='xhs',
            row_id='row2',
            dentry_uuid='test-dentry-uuid',
            id_or_name='test-table',
            update_count=1,
            threshold='否',
            author_work=mock_low_engagement_work,
            submit_user=[
                {'unionId': 'user-3', 'name': 'Test User 3'}
            ]
        )
    ]


def test_notification_factory():
    """Test the notification factory."""
    print("=" * 60)
    print("Testing Notification Factory")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        # Test auto-detection (should detect dingtalk)
        config = create_test_config('dingtalk')
        notification_handler = create_notification_handler(config, logger)
        
        assert isinstance(notification_handler, DingTalkNotification), f"Expected DingTalkNotification, got {type(notification_handler)}"
        print("✅ DingTalk notification handler created successfully (auto-detected)")
        
        # Test explicit type
        notification_handler = create_notification_handler(config, logger, 'email')
        assert isinstance(notification_handler, EmailNotification), f"Expected EmailNotification, got {type(notification_handler)}"
        print("✅ Email notification handler created successfully (explicit)")
        
        # Test unsupported notification type
        try:
            notification_handler = create_notification_handler(config, logger, 'unsupported_type')
            assert False, "Should have raised exception for unsupported type"
        except Exception as e:
            print(f"✅ Correctly raised exception for unsupported type: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"Factory test failed: {e}")
        return False


def test_threshold_checking():
    """Test threshold checking functionality."""
    print("\n" + "=" * 60)
    print("Testing Threshold Checking")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config('dingtalk')
        notification_handler = create_notification_handler(config, logger, 'dingtalk')
        
        # Create test works
        test_works = create_test_works()
        high_engagement_work = test_works[0]
        low_engagement_work = test_works[1]
        
        # Test threshold checking
        notification_config = config.notification_config
        
        # Check high engagement work (should meet threshold)
        high_result = notification_handler._check_threshold(high_engagement_work, notification_config)
        assert high_result['meets_threshold'] == True, "High engagement work should meet threshold"
        assert 'like_count' in high_result['threshold_checks'], "Should have like_count check"
        assert high_result['threshold_checks']['like_count']['meets_threshold'] == True, "Like count should meet threshold"
        
        # Check low engagement work (should not meet threshold)
        low_result = notification_handler._check_threshold(low_engagement_work, notification_config)
        assert low_result['meets_threshold'] == False, "Low engagement work should not meet threshold"
        
        print("✅ Threshold checking test passed")
        print(f"   High engagement meets threshold: {high_result['meets_threshold']}")
        print(f"   Low engagement meets threshold: {low_result['meets_threshold']}")
        print(f"   High engagement like count: {high_result['threshold_checks']['like_count']['current_value']}")
        print(f"   Low engagement like count: {low_result['threshold_checks']['like_count']['current_value']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Threshold checking test failed: {e}")
        return False


def test_message_building():
    """Test message building functionality."""
    print("\n" + "=" * 60)
    print("Testing Message Building")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config('dingtalk')
        notification_handler = create_notification_handler(config, logger, 'dingtalk')
        
        # Create test work
        test_works = create_test_works()
        test_work = test_works[0]  # High engagement work
        
        # Get notification config and threshold result
        notification_config = config.notification_config
        threshold_result = notification_handler._check_threshold(test_work, notification_config)
        
        # Build message
        message = notification_handler._build_notification_message(
            test_work, notification_config, threshold_result
        )
        
        # Verify message content
        assert "High Engagement Work" in message, "Message should contain work title"
        assert "Popular Author" in message, "Message should contain author name"
        assert "小红书" in message, "Message should contain platform name"
        assert "点赞数=150" in message, "Message should contain like count"
        
        print("✅ Message building test passed")
        print(f"   Message length: {len(message)}")
        print(f"   Message preview: {message[:100]}...")
        
        return True
        
    except Exception as e:
        logger.error(f"Message building test failed: {e}")
        return False


def test_dingtalk_notification():
    """Test DingTalk notification with mocked dependencies."""
    print("\n" + "=" * 60)
    print("Testing DingTalk Notification")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config('dingtalk')
        notification_handler = create_notification_handler(config, logger, 'dingtalk')
        
        # Mock the DingTalk client
        mock_client = Mock()
        mock_client.send_message.return_value = True
        notification_handler._dingtalk_client = mock_client
        
        # Create test context
        context = WorkContext(
            client_name='test-client',
            batch_id='test-batch-001',
            client_config=config
        )
        
        # Set synced works (only high engagement work should trigger notification)
        context.synced_works = create_test_works()
        
        # Process notifications
        result = notification_handler.handle(context)
        
        assert result == True, "Handler should return True"
        assert context.notifications_sent == 1, f"Expected 1 notification sent, got {context.notifications_sent}"
        assert len(context.notification_results) == 2, f"Expected 2 notification results, got {len(context.notification_results)}"
        
        # Verify that DingTalk client was called
        assert mock_client.send_message.called, "DingTalk client should have been called"
        
        # Check notification results
        high_engagement_result = next((r for r in context.notification_results if r['work_id'] == 'high-engagement'), None)
        low_engagement_result = next((r for r in context.notification_results if r['work_id'] == 'low-engagement'), None)
        
        assert high_engagement_result is not None, "Should have result for high engagement work"
        assert low_engagement_result is not None, "Should have result for low engagement work"
        
        assert high_engagement_result['success'] == True, "High engagement notification should succeed"
        assert high_engagement_result['sent'] == True, "High engagement notification should be sent"
        
        assert low_engagement_result['success'] == True, "Low engagement processing should succeed"
        assert low_engagement_result['sent'] == False, "Low engagement notification should not be sent"
        assert low_engagement_result['reason'] == 'threshold_not_met', "Should indicate threshold not met"
        
        print(f"✅ DingTalk notification test passed")
        print(f"   Notifications sent: {context.notifications_sent}")
        print(f"   DingTalk calls: {mock_client.send_message.call_count}")
        print(f"   High engagement sent: {high_engagement_result['sent']}")
        print(f"   Low engagement reason: {low_engagement_result['reason']}")
        
        return True
        
    except Exception as e:
        logger.error(f"DingTalk notification test failed: {e}")
        return False


def test_recipient_handling():
    """Test recipient handling functionality."""
    print("\n" + "=" * 60)
    print("Testing Recipient Handling")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config('dingtalk')
        notification_handler = create_notification_handler(config, logger, 'dingtalk')
        
        # Create test work with recipients
        test_works = create_test_works()
        test_work = test_works[0]  # Has submit_user
        
        # Get recipients
        notification_config = config.notification_config
        recipients = notification_handler._get_notification_recipients(test_work, notification_config)
        
        # Should include submit_user + default_recipients, with duplicates removed
        assert len(recipients) >= 2, f"Expected at least 2 recipients, got {len(recipients)}"
        
        # Check that all recipients have unionId
        for recipient in recipients:
            assert 'unionId' in recipient, f"Recipient missing unionId: {recipient}"
        
        # Check for duplicates
        union_ids = [r['unionId'] for r in recipients]
        assert len(union_ids) == len(set(union_ids)), "Recipients should not have duplicates"
        
        print("✅ Recipient handling test passed")
        print(f"   Total recipients: {len(recipients)}")
        print(f"   Unique union IDs: {len(set(union_ids))}")
        print(f"   Recipients: {[r['unionId'] for r in recipients]}")
        
        return True
        
    except Exception as e:
        logger.error(f"Recipient handling test failed: {e}")
        return False


def test_error_handling():
    """Test error handling in notifications."""
    print("\n" + "=" * 60)
    print("Testing Error Handling")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config('dingtalk')
        notification_handler = create_notification_handler(config, logger, 'dingtalk')
        
        # Mock client that fails
        mock_client = Mock()
        mock_client.send_message.side_effect = Exception("Mock notification error")
        notification_handler._dingtalk_client = mock_client
        
        # Create test context
        context = WorkContext(
            client_name='test-client',
            batch_id='test-batch-error',
            client_config=config
        )
        
        # Set synced works (only high engagement work)
        context.synced_works = [create_test_works()[0]]  # High engagement work
        
        # Process notifications (should handle errors gracefully)
        result = notification_handler.handle(context)
        
        # Should continue chain even with notification errors
        assert result == True, "Handler should return True even with notification errors"
        assert context.notifications_sent == 0, f"Expected 0 notifications sent, got {context.notifications_sent}"
        assert len(context.notification_results) == 1, f"Expected 1 notification result, got {len(context.notification_results)}"
        
        # Check that error was recorded
        notification_result = context.notification_results[0]
        assert notification_result['success'] == True, "Processing should succeed"
        assert notification_result['sent'] == False, "Notification should not be sent due to error"
        
        print("✅ Error handling test passed")
        print(f"   Notifications sent: {context.notifications_sent}")
        print(f"   Processing succeeded: {notification_result['success']}")
        print(f"   Notification sent: {notification_result['sent']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error handling test failed: {e}")
        return False


def main():
    """Main test function."""
    print("=" * 60)
    print("Testing Notification Handlers")
    print("=" * 60)
    
    tests = [
        test_notification_factory,
        test_threshold_checking,
        test_message_building,
        test_dingtalk_notification,
        test_recipient_handling,
        test_error_handling
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("TEST RESULTS")
    print("=" * 60)
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total: {passed + failed}")
    
    if failed == 0:
        print("✅ All tests passed!")
        return True
    else:
        print("❌ Some tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
