#!/usr/bin/env python3
"""
Integration test for work updater in the complete chain of responsibility.
"""

import logging
import sys
from datetime import datetime
from unittest.mock import Mock, MagicMock, AsyncMock

# Add current directory to path for imports
sys.path.insert(0, '.')

from core.context import Work<PERSON>ontext, ClientConfig
from core.handlers.data_fetcher_factory import create_data_fetcher
from core.handlers.work_updater_factory import create_work_updater
from core.chain_executor import ChainExecutor
from models.work_models import WorkDetailTaskModel
from core.exceptions import WorkUpdateException


class MockDataSync:
    """Mock data sync handler for testing."""
    
    def __init__(self, client_config, logger):
        self.client_config = client_config
        self.logger = logger
        self.next_handler = None
        self.handler_name = self.__class__.__name__
    
    def set_next(self, handler):
        self.next_handler = handler
        return handler
    
    def handle(self, context):
        self.logger.info("MockDataSync: Syncing updated works")
        
        # Simulate syncing data
        context.synced_works = context.updated_works.copy()
        
        self.logger.info(f"MockDataSync: Synced {len(context.synced_works)} works")
        
        if self.next_handler:
            return self.next_handler.handle(context)
        return True


def setup_test_logging():
    """Setup logging for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger('test_integration')


def create_test_config():
    """Create a test client configuration."""
    config_data = {
        'name': 'test-client',
        'data_fetcher': {
            'source_type': 'dingtalk_bitable',
            'filter_rules': {
                'update_times_limit': 5
            }
        },
        'work_updater': {
            'platforms': ['xhs', 'dy'],
            'batch_size': 2
        },
        'user_info': {
            'api-token': 'test-token',
            'user_id': 123,
            'tenant': 'test-tenant'
        },
        'bitable': {
            'dentryUuid': 'test-dentry-uuid',
            'idOrName': 'test-table',
            'webhook': 'test-webhook'
        }
    }
    
    return ClientConfig.from_dict(config_data)


def test_complete_chain_integration():
    """Test complete chain with data fetcher and work updater."""
    print("=" * 60)
    print("Testing Complete Chain Integration")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        # Create test configuration
        config = create_test_config()
        
        # Create handlers
        data_fetcher = create_data_fetcher(config, logger)
        work_updater = create_work_updater(config, logger, 'basic')  # Use basic to avoid DB dependencies
        data_sync = MockDataSync(config, logger)
        
        # Mock the bitable client for data fetcher
        mock_bitable_client = Mock()
        mock_records = [
            Mock(
                work_url='https://www.xiaohongshu.com/discovery/item/686e6a68000000002203d213',
                update_count=2,
                user_union_id='test-user',
                row_id='row123',
                extends={}
            ),
            Mock(
                work_url='https://www.douyin.com/video/1234567890123456789',
                update_count=1,
                user_union_id='test-user2',
                row_id='row456',
                extends={}
            )
        ]
        
        mock_bitable_client.list_bitable_data_by_api_filter.return_value = mock_records
        data_fetcher._bitable_client = mock_bitable_client
        
        # Mock platform handlers for work updater
        mock_xhs_handler = Mock()
        mock_dy_handler = AsyncMock()
        
        # Create mock work details
        mock_xhs_work = Mock()
        mock_xhs_work.title = "Test XHS Title"
        mock_xhs_work.like_count = 150
        mock_xhs_work.comment_count = 30
        mock_xhs_work.author_name = "Test XHS Author"
        mock_xhs_work.work_id = "686e6a68000000002203d213"
        mock_xhs_work.platform = "xhs"
        
        mock_dy_work = Mock()
        mock_dy_work.title = "Test DY Title"
        mock_dy_work.like_count = 250
        mock_dy_work.comment_count = 60
        mock_dy_work.author_name = "Test DY Author"
        mock_dy_work.work_id = "1234567890123456789"
        mock_dy_work.platform = "dy"
        
        # Configure mock handlers
        mock_xhs_handler.query_article_detail.return_value = mock_xhs_work
        mock_dy_handler.query_article_detail.return_value = mock_dy_work
        
        # Replace platform handlers
        work_updater._platform_handlers = {
            'xhs': mock_xhs_handler,
            'dy': mock_dy_handler
        }
        
        # Chain handlers together
        data_fetcher.set_next(work_updater).set_next(data_sync)
        
        # Create chain executor
        executor = ChainExecutor(config, logger)
        executor.set_chain(data_fetcher)
        
        # Execute the complete chain
        context = executor.execute("test-batch-integration")
        
        # Verify results at each stage
        assert len(context.raw_tasks) == 2, f"Expected 2 raw tasks, got {len(context.raw_tasks)}"
        assert len(context.formatted_tasks) == 2, f"Expected 2 formatted tasks, got {len(context.formatted_tasks)}"
        assert len(context.updated_works) == 2, f"Expected 2 updated works, got {len(context.updated_works)}"
        assert len(context.synced_works) == 2, f"Expected 2 synced works, got {len(context.synced_works)}"
        
        # Check that work details were added
        for work in context.updated_works:
            assert work.author_work is not None, f"Work {work.work_url} should have author_work"
            assert hasattr(work.author_work, 'title'), "Author work should have title"
            assert hasattr(work.author_work, 'like_count'), "Author work should have like_count"
        
        # Check specific work details
        xhs_work = next((w for w in context.updated_works if w.platform == 'xhs'), None)
        dy_work = next((w for w in context.updated_works if w.platform == 'dy'), None)
        
        assert xhs_work is not None, "XHS work not found"
        assert dy_work is not None, "DY work not found"
        
        assert xhs_work.author_work.title == "Test XHS Title"
        assert xhs_work.author_work.like_count == 150
        assert dy_work.author_work.title == "Test DY Title"
        assert dy_work.author_work.like_count == 250
        
        print("✅ Complete chain integration test passed")
        print(f"   Raw tasks: {len(context.raw_tasks)}")
        print(f"   Formatted tasks: {len(context.formatted_tasks)}")
        print(f"   Updated works: {len(context.updated_works)}")
        print(f"   Synced works: {len(context.synced_works)}")
        print(f"   XHS work title: {xhs_work.author_work.title}")
        print(f"   DY work title: {dy_work.author_work.title}")
        
        # Check execution summary
        summary = context.to_summary()
        print(f"\nExecution Summary:")
        print(f"   Execution time: {summary['execution_time']:.3f}s")
        print(f"   Success rate: {summary['success_rate']:.1f}%")
        print(f"   Total processed: {summary['total_processed']}")
        print(f"   Successful updates: {summary['successful_updates']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Complete chain integration test failed: {e}")
        return False


def test_async_processing():
    """Test async processing with Douyin handler."""
    print("\n" + "=" * 60)
    print("Testing Async Processing")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config()
        work_updater = create_work_updater(config, logger, 'basic')
        
        # Mock async Douyin handler
        mock_dy_handler = AsyncMock()
        mock_dy_work = Mock()
        mock_dy_work.title = "Async DY Title"
        mock_dy_work.like_count = 300
        mock_dy_work.comment_count = 80
        mock_dy_work.author_name = "Async DY Author"
        mock_dy_work.work_id = "1234567890123456789"
        
        mock_dy_handler.query_article_detail.return_value = mock_dy_work
        work_updater._platform_handlers = {'dy': mock_dy_handler}
        
        # Create context with DY tasks only
        context = WorkContext(
            client_name='test-client',
            batch_id='test-batch-async',
            client_config=config
        )
        
        context.formatted_tasks = [
            WorkDetailTaskModel(
                record_id='test-batch-async',
                work_url='https://www.douyin.com/video/1234567890123456789',
                work_id='1234567890123456789',
                platform='dy',
                row_id='row1',
                update_count=1
            ),
            WorkDetailTaskModel(
                record_id='test-batch-async',
                work_url='https://www.douyin.com/video/9876543210987654321',
                work_id='9876543210987654321',
                platform='dy',
                row_id='row2',
                update_count=2
            )
        ]
        
        # Process tasks
        result = work_updater.handle(context)
        
        assert result == True, "Handler should return True"
        assert len(context.updated_works) == 2, f"Expected 2 updated works, got {len(context.updated_works)}"
        assert context.successful_updates == 2, f"Expected 2 successful updates, got {context.successful_updates}"
        
        # Verify async handler was called
        assert mock_dy_handler.query_article_detail.call_count == 2, f"Expected 2 async calls, got {mock_dy_handler.query_article_detail.call_count}"
        
        print("✅ Async processing test passed")
        print(f"   Async tasks processed: {len(context.updated_works)}")
        print(f"   Async handler calls: {mock_dy_handler.query_article_detail.call_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"Async processing test failed: {e}")
        return False


def test_mixed_sync_async_processing():
    """Test mixed sync and async processing."""
    print("\n" + "=" * 60)
    print("Testing Mixed Sync/Async Processing")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config()
        work_updater = create_work_updater(config, logger, 'basic')
        
        # Mock both sync and async handlers
        mock_xhs_handler = Mock()  # Sync
        mock_dy_handler = AsyncMock()  # Async
        
        mock_xhs_work = Mock()
        mock_xhs_work.title = "Mixed XHS Title"
        mock_xhs_work.like_count = 100
        
        mock_dy_work = Mock()
        mock_dy_work.title = "Mixed DY Title"
        mock_dy_work.like_count = 200
        
        mock_xhs_handler.query_article_detail.return_value = mock_xhs_work
        mock_dy_handler.query_article_detail.return_value = mock_dy_work
        
        work_updater._platform_handlers = {
            'xhs': mock_xhs_handler,
            'dy': mock_dy_handler
        }
        
        # Create context with mixed tasks
        context = WorkContext(
            client_name='test-client',
            batch_id='test-batch-mixed',
            client_config=config
        )
        
        context.formatted_tasks = [
            WorkDetailTaskModel(
                record_id='test-batch-mixed',
                work_url='https://www.xiaohongshu.com/discovery/item/686e6a68000000002203d213',
                work_id='686e6a68000000002203d213',
                platform='xhs',
                row_id='row1',
                update_count=1
            ),
            WorkDetailTaskModel(
                record_id='test-batch-mixed',
                work_url='https://www.douyin.com/video/1234567890123456789',
                work_id='1234567890123456789',
                platform='dy',
                row_id='row2',
                update_count=2
            )
        ]
        
        # Process tasks
        result = work_updater.handle(context)
        
        assert result == True, "Handler should return True"
        assert len(context.updated_works) == 2, f"Expected 2 updated works, got {len(context.updated_works)}"
        assert context.successful_updates == 2, f"Expected 2 successful updates, got {context.successful_updates}"
        
        # Verify both handlers were called
        assert mock_xhs_handler.query_article_detail.call_count == 1, f"Expected 1 sync call, got {mock_xhs_handler.query_article_detail.call_count}"
        assert mock_dy_handler.query_article_detail.call_count == 1, f"Expected 1 async call, got {mock_dy_handler.query_article_detail.call_count}"
        
        print("✅ Mixed sync/async processing test passed")
        print(f"   Mixed tasks processed: {len(context.updated_works)}")
        print(f"   Sync handler calls: {mock_xhs_handler.query_article_detail.call_count}")
        print(f"   Async handler calls: {mock_dy_handler.query_article_detail.call_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"Mixed processing test failed: {e}")
        return False


def main():
    """Main test function."""
    print("=" * 60)
    print("Work Updater Integration Tests")
    print("=" * 60)
    
    tests = [
        test_complete_chain_integration,
        test_async_processing,
        test_mixed_sync_async_processing
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("INTEGRATION TEST RESULTS")
    print("=" * 60)
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total: {passed + failed}")
    
    if failed == 0:
        print("✅ All integration tests passed!")
        return True
    else:
        print("❌ Some integration tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
