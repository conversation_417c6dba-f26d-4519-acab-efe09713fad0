#!/usr/bin/env python3
"""
Test script for data fetcher handlers.
"""

import logging
import sys
from datetime import datetime
from unittest.mock import Mock, MagicMock

# Add current directory to path for imports
sys.path.insert(0, '.')

from core.context import WorkContext, ClientConfig
from core.handlers.data_fetcher_factory import create_data_fetcher
from core.handlers.dingtalk_data_fetcher import DingTalkDataFetcher
from core.handlers.service_client_data_fetcher import ServiceClientDataFetcher
from core.exceptions import DataFetchException


def setup_test_logging():
    """Setup logging for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger('test_data_fetcher')


def create_test_config(source_type='dingtalk_bitable'):
    """Create a test client configuration."""
    config_data = {
        'name': 'test-client',
        'data_fetcher': {
            'source_type': source_type,
            'filter_rules': {
                'update_times_limit': 5
            }
        },
        'work_updater': {
            'platforms': ['xhs', 'dy'],
            'batch_size': 50
        },
        'user_info': {
            'api-token': 'test-token',
            'user_id': 123,
            'tenant': 'test-tenant'
        },
        'bitable': {
            'dentryUuid': 'test-dentry-uuid',
            'idOrName': 'test-table',
            'webhook': 'test-webhook'
        }
    }
    
    return ClientConfig.from_dict(config_data)


def test_data_fetcher_factory():
    """Test the data fetcher factory."""
    print("=" * 60)
    print("Testing Data Fetcher Factory")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        # Test DingTalk fetcher creation
        config = create_test_config('dingtalk_bitable')
        fetcher = create_data_fetcher(config, logger)
        
        assert isinstance(fetcher, DingTalkDataFetcher), f"Expected DingTalkDataFetcher, got {type(fetcher)}"
        print("✅ DingTalk data fetcher created successfully")
        
        # Test Service Client fetcher creation
        config = create_test_config('service_client')
        fetcher = create_data_fetcher(config, logger)
        
        assert isinstance(fetcher, ServiceClientDataFetcher), f"Expected ServiceClientDataFetcher, got {type(fetcher)}"
        print("✅ Service Client data fetcher created successfully")
        
        # Test unsupported source type
        try:
            config = create_test_config('unsupported_type')
            fetcher = create_data_fetcher(config, logger)
            assert False, "Should have raised exception for unsupported type"
        except Exception as e:
            print(f"✅ Correctly raised exception for unsupported type: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"Factory test failed: {e}")
        return False


def test_dingtalk_data_fetcher():
    """Test DingTalk data fetcher with mocked dependencies."""
    print("\n" + "=" * 60)
    print("Testing DingTalk Data Fetcher")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        # Create test configuration
        config = create_test_config('dingtalk_bitable')
        
        # Create fetcher
        fetcher = DingTalkDataFetcher(config, logger)
        
        # Mock the bitable client
        mock_client = Mock()
        mock_records = [
            Mock(
                work_url='https://www.xiaohongshu.com/discovery/item/686e6a68000000002203d213',
                update_count=2,
                user_union_id='test-user',
                row_id='row123',
                extends={}
            ),
            Mock(
                work_url='https://www.douyin.com/video/1234567890123456789',
                update_count=1,
                user_union_id='test-user2',
                row_id='row456',
                extends={}
            )
        ]
        
        mock_client.list_bitable_data_by_api_filter.return_value = mock_records
        fetcher._bitable_client = mock_client
        
        # Create test context
        context = WorkContext(
            client_name='test-client',
            batch_id='test-batch-001',
            client_config=config
        )
        
        # Test data fetching
        raw_data = fetcher.fetch_raw_data(
            context, 
            'dingtalk_bitable', 
            {'update_times_limit': 5}, 
            100
        )
        
        assert len(raw_data) == 2, f"Expected 2 records, got {len(raw_data)}"
        assert raw_data[0]['work_url'] == 'https://www.xiaohongshu.com/discovery/item/686e6a68000000002203d213'
        assert raw_data[1]['work_url'] == 'https://www.douyin.com/video/1234567890123456789'
        
        print(f"✅ Successfully fetched {len(raw_data)} records")
        print(f"   Record 1: {raw_data[0]['work_url']}")
        print(f"   Record 2: {raw_data[1]['work_url']}")
        
        return True
        
    except Exception as e:
        logger.error(f"DingTalk fetcher test failed: {e}")
        return False


def test_url_extraction():
    """Test URL extraction and platform detection."""
    print("\n" + "=" * 60)
    print("Testing URL Extraction")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config()
        fetcher = DingTalkDataFetcher(config, logger)
        
        # Test cases (using valid format IDs)
        test_urls = [
            ('https://www.xiaohongshu.com/discovery/item/686e6a68000000002203d213', 'xhs', '686e6a68000000002203d213'),
            ('https://www.douyin.com/video/1234567890123456789', 'dy', '1234567890123456789'),
            ('https://invalid-url.com/test', None, None),
        ]
        
        for url, expected_platform, expected_work_id in test_urls:
            result = fetcher.extract_platform_info(url)
            
            if expected_platform is None:
                assert result is None, f"Expected None for {url}, got {result}"
                print(f"✅ Correctly rejected invalid URL: {url}")
            else:
                assert result is not None, f"Expected result for {url}, got None"
                platform, work_id = result
                assert platform == expected_platform, f"Expected platform {expected_platform}, got {platform}"
                assert work_id == expected_work_id, f"Expected work_id {expected_work_id}, got {work_id}"
                print(f"✅ Correctly extracted {platform}:{work_id} from {url}")
        
        return True
        
    except Exception as e:
        logger.error(f"URL extraction test failed: {e}")
        return False


def test_task_model_creation():
    """Test task model creation from raw data."""
    print("\n" + "=" * 60)
    print("Testing Task Model Creation")
    print("=" * 60)
    
    logger = setup_test_logging()
    
    try:
        config = create_test_config()
        fetcher = DingTalkDataFetcher(config, logger)
        
        # Create test context
        context = WorkContext(
            client_name='test-client',
            batch_id='test-batch-001',
            client_config=config
        )
        
        # Test raw data
        raw_records = [
            {
                'work_url': 'https://www.xiaohongshu.com/discovery/item/686e6a68000000002203d213',
                'update_count': 2,
                'submit_user': 'test-user',
                'row_id': 'row123',
                'dentry_uuid': 'test-dentry',
                'id_or_name': 'test-table',
                'extends': {'extra': 'data'}
            }
        ]
        
        # Convert to task models
        task_models = fetcher.convert_to_task_models(context, raw_records)
        
        assert len(task_models) == 1, f"Expected 1 task model, got {len(task_models)}"
        
        task = task_models[0]
        assert task.work_url == 'https://www.xiaohongshu.com/discovery/item/686e6a68000000002203d213'
        assert task.platform == 'xhs'
        assert task.work_id == '686e6a68000000002203d213'
        assert task.row_id == 'row123'
        assert task.update_count == 2
        
        print("✅ Successfully created task model:")
        print(f"   URL: {task.work_url}")
        print(f"   Platform: {task.platform}")
        print(f"   Work ID: {task.work_id}")
        print(f"   Update Count: {task.update_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"Task model creation test failed: {e}")
        return False


def main():
    """Main test function."""
    print("=" * 60)
    print("Testing Data Fetcher Handlers")
    print("=" * 60)
    
    tests = [
        test_data_fetcher_factory,
        test_dingtalk_data_fetcher,
        test_url_extraction,
        test_task_model_creation
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("TEST RESULTS")
    print("=" * 60)
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total: {passed + failed}")
    
    if failed == 0:
        print("✅ All tests passed!")
        return True
    else:
        print("❌ Some tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
