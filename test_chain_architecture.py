#!/usr/bin/env python3
"""
Simple test script to verify the chain architecture works correctly.
"""

import logging
import sys
from datetime import datetime

# Add current directory to path for imports
sys.path.insert(0, '.')

from core.context import WorkContext, ClientConfig
from core.handlers.base_handler import <PERSON><PERSON>andler
from core.chain_executor import ChainExecutor
from core.exceptions import HandlerException


class TestDataFetcher(BaseHandler):
    """Test implementation of data fetcher."""
    
    def handle(self, context: WorkContext) -> bool:
        self.logger.info("TestDataFetcher: Fetching test data")
        
        # Simulate fetching some tasks
        context.raw_tasks = [
            {"work_url": "https://www.xiaohongshu.com/discovery/item/test1", "platform": "xhs"},
            {"work_url": "https://www.douyin.com/video/test2", "platform": "dy"}
        ]
        
        context.total_processed = len(context.raw_tasks)
        self.logger.info(f"TestDataFetcher: Fetched {len(context.raw_tasks)} tasks")
        
        return self.handle_next(context)


class TestWorkUpdater(BaseHandler):
    """Test implementation of work updater."""
    
    def handle(self, context: WorkContext) -> bool:
        self.logger.info("TestWorkUpdater: Updating work details")
        
        # Simulate updating work details
        for task in context.raw_tasks:
            updated_task = task.copy()
            updated_task.update({
                "title": f"Test Title for {task['platform']}",
                "like_count": 100,
                "comment_count": 20
            })
            context.updated_works.append(updated_task)
        
        context.successful_updates = len(context.updated_works)
        self.logger.info(f"TestWorkUpdater: Updated {len(context.updated_works)} works")
        
        return self.handle_next(context)


class TestDataSync(BaseHandler):
    """Test implementation of data sync."""
    
    def handle(self, context: WorkContext) -> bool:
        self.logger.info("TestDataSync: Syncing data")
        
        # Simulate syncing data
        context.synced_works = context.updated_works.copy()
        
        self.logger.info(f"TestDataSync: Synced {len(context.synced_works)} works")
        
        return self.handle_next(context)


class TestNotifier(BaseHandler):
    """Test implementation of notifier."""
    
    def handle(self, context: WorkContext) -> bool:
        self.logger.info("TestNotifier: Sending notifications")
        
        # Simulate checking thresholds and sending notifications
        threshold = self.client_config.notification_thresholds.get('like_count', 50)
        
        for work in context.synced_works:
            if work.get('like_count', 0) >= threshold:
                context.notifications_sent += 1
                self.logger.info(f"TestNotifier: Sent notification for work with {work['like_count']} likes")
        
        self.logger.info(f"TestNotifier: Sent {context.notifications_sent} notifications")
        
        return True  # End of chain


def setup_test_logging():
    """Setup logging for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger('test_chain')


def create_test_config():
    """Create a test client configuration."""
    return ClientConfig(
        name="test-client",
        schedule_cron="0 */2 * * *",
        max_tasks_per_run=100,
        supported_platforms=["xhs", "dy"],
        notification_thresholds={"like_count": 50}
    )


def build_test_chain(config, logger):
    """Build a test handler chain."""
    # Create handlers
    fetcher = TestDataFetcher(config, logger)
    updater = TestWorkUpdater(config, logger)
    syncer = TestDataSync(config, logger)
    notifier = TestNotifier(config, logger)
    
    # Chain them together
    fetcher.set_next(updater).set_next(syncer).set_next(notifier)
    
    return fetcher


def main():
    """Main test function."""
    print("=" * 60)
    print("Testing Chain of Responsibility Architecture")
    print("=" * 60)
    
    # Setup
    logger = setup_test_logging()
    config = create_test_config()
    
    try:
        # Build chain
        chain_head = build_test_chain(config, logger)
        
        # Create executor
        executor = ChainExecutor(config, logger)
        executor.set_chain(chain_head)
        
        # Validate chain
        validation_errors = executor.validate_chain()
        if validation_errors:
            print("Chain validation failed:")
            for error in validation_errors:
                print(f"  - {error}")
            return False
        
        print("✅ Chain validation passed")
        print()
        
        # Execute chain
        print("Executing chain...")
        context = executor.execute("test-batch-001")
        
        print()
        print("=" * 60)
        print("EXECUTION RESULTS")
        print("=" * 60)
        
        summary = context.to_summary()
        for key, value in summary.items():
            print(f"{key}: {value}")
        
        print()
        if context.error_messages:
            print("Errors:")
            for error in context.error_messages:
                print(f"  - {error}")
        else:
            print("✅ No errors occurred")
        
        print()
        print("✅ Chain architecture test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
